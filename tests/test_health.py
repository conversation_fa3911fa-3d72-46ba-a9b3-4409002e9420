"""
Testes para endpoints de health check.
"""

import pytest
from fastapi.testclient import TestClient


def test_basic_health_check(test_client: TestClient):
    """Testa o health check básico."""
    response = test_client.get("/health")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "healthy"
    assert data["service"] == "Chat IA Agno Financeiro"
    assert data["version"] == "0.1.0"
    assert "timestamp" in data


def test_root_endpoint(test_client: TestClient):
    """Testa o endpoint raiz."""
    response = test_client.get("/")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["message"] == "Chat IA Agno Financeiro API"
    assert data["version"] == "0.1.0"
    assert data["docs"] == "/docs"
    assert data["health"] == "/health"


@pytest.mark.asyncio
async def test_health_endpoints_async(async_client):
    """Testa endpoints de health de forma assíncrona."""
    # Health check básico
    response = await async_client.get("/health")
    assert response.status_code == 200
    
    # API health check
    response = await async_client.get("/api/v1/health/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
