"""
Configurações e fixtures para testes.
"""

import pytest
import asyncio
from typing import AsyncGenerator
from httpx import AsyncClient
from fastapi.testclient import TestClient

from src.main import app
from src.core.config import get_settings


@pytest.fixture(scope="session")
def event_loop():
    """Cria um event loop para toda a sessão de testes."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_client():
    """Cliente de teste síncrono."""
    return TestClient(app)


@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Cliente de teste assíncrono."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def test_settings():
    """Configurações para testes."""
    return get_settings()


@pytest.fixture
def sample_user_data():
    """Dados de usuário para testes."""
    return {
        "user_id": "test-user-123",
        "username": "testuser",
        "email": "<EMAIL>"
    }


@pytest.fixture
def sample_chat_request():
    """Requisição de chat para testes."""
    return {
        "message": "Qual é o preço do Bitcoin hoje?",
        "user_id": "test-user-123",
        "complexity": "simple",
        "asset_class": "crypto"
    }
