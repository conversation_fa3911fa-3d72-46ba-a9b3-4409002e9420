# Arquitetura do Sistema Chat IA Agno Financeiro

## Visão Geral

O Chat IA Agno Financeiro é um sistema de inteligência artificial especializado em análise financeira, construído com uma arquitetura de agentes especializados que trabalham de forma coordenada para fornecer insights sobre diferentes classes de ativos.

## Arquitetura de Alto Nível

```
┌─────────────────────────────────────────────────────────────┐
│                    Interface de Chat                        │
│                  (FastAPI + WebSockets)                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Agente Orquestrador                          │
│              (Meta-Agente de Roteamento)                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌──────▼──────┐
│   Agente     │ │ Agente  │ │   Agente    │
│     de       │ │   de    │ │     de      │
│Criptomoedas  │ │ Ações   │ │   Ações     │
│              │ │   US    │ │    BR       │
└──────────────┘ └─────────┘ └─────────────┘
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌──────▼──────┐
│   Dados      │ │ Dados   │ │   Dados     │
│   Crypto     │ │   US    │ │     BR      │
│              │ │ Market  │ │   Market    │
└──────────────┘ └─────────┘ └─────────────┘
```

## Componentes Principais

### 1. Motor de IA Híbrido

**Localização**: `src/core/llm/`

O sistema utiliza múltiplos LLMs de forma inteligente:

- **OpenAI GPT-4**: Para análises complexas e versatilidade
- **Anthropic Claude**: Para análises profundas e contexto longo
- **Google Gemini**: Para custo-benefício e contextos extensos

**Roteamento Inteligente**:
- Tarefas simples → GPT-4 Mini (custo otimizado)
- Tarefas médias → Gemini Pro (balanceado)
- Tarefas complexas → Claude Sonnet (análise profunda)

### 2. Sistema MCP Memory

**Localização**: `src/core/memory/`

Implementa o protocolo MCP (Model Context Protocol) para:
- Persistência de contexto conversacional
- Memória de longo prazo por usuário
- Cache inteligente de respostas
- Histórico de interações

**Providers Suportados**:
- Redis (padrão)
- PostgreSQL (futuro)
- Memória local (desenvolvimento)

### 3. Arquitetura de Agentes Agno

**Localização**: `src/agents/`

#### 3.1 Agente Orquestrador (Meta-Agente)
- **Função**: Roteamento inteligente de consultas
- **Capacidades**:
  - NLU/PLN avançado (português/inglês)
  - Reconhecimento de entidades financeiras
  - Classificação de complexidade
  - Coordenação entre agentes

#### 3.2 Agente de Criptomoedas
- **Especialização**: Análise de ativos digitais
- **Fontes de Dados**:
  - CoinGecko API
  - CoinMarketCap API
  - Binance API
  - Análise on-chain
- **Capacidades**:
  - Análise técnica de crypto
  - Sentiment analysis de redes sociais
  - Comparação de altcoins
  - Monitoramento de exchanges

#### 3.3 Agente de Ações Americanas
- **Especialização**: Mercado dos EUA (NYSE, NASDAQ)
- **Fontes de Dados**:
  - Finnhub API
  - Alpha Vantage API
  - SEC EDGAR API
  - FRED (Federal Reserve)
  - NewsAPI
- **Capacidades**:
  - Análise fundamentalista SEC/EDGAR
  - Dados de earnings
  - Indicadores econômicos
  - Análise setorial

#### 3.4 Agente de Ações Brasileiras
- **Especialização**: Mercado brasileiro (B3)
- **Fontes de Dados**:
  - B3 API
  - CVM (dados.cvm.gov.br)
  - BCB API
  - IBGE API
  - Cedro Technologies
- **Capacidades**:
  - PLN especializado em português
  - Análise fundamentalista brasileira
  - Indicadores econômicos nacionais
  - Notícias em português

## Fluxo de Processamento

### 1. Recepção da Consulta
```
Usuário → FastAPI → Validação → Agente Orquestrador
```

### 2. Análise e Roteamento
```python
# Exemplo de análise no Orquestrador
analysis = {
    "asset_class": "crypto",
    "query_type": "price_query", 
    "symbols": ["BTC", "ETH"],
    "complexity": "simple",
    "language": "pt"
}
```

### 3. Processamento Especializado
```
Orquestrador → Agente Especializado → APIs de Dados → LLM → Resposta
```

### 4. Resposta e Memória
```
Resposta → MCP Memory → Cache → Usuário
```

## Tecnologias Utilizadas

### Backend
- **FastAPI**: Framework web assíncrono
- **SQLAlchemy**: ORM para PostgreSQL
- **Alembic**: Migrations de banco
- **Redis**: Cache e sessões
- **Pydantic**: Validação de dados

### IA e ML
- **OpenAI API**: GPT-4 e variantes
- **Anthropic API**: Claude models
- **Google AI API**: Gemini models
- **LangChain**: Orquestração de LLMs
- **NLTK/spaCy**: Processamento de linguagem

### Dados Financeiros
- **Finnhub**: Dados de mercado US
- **Alpha Vantage**: Dados históricos
- **CoinGecko**: Dados de crypto
- **B3/CVM**: Dados brasileiros
- **FRED**: Indicadores econômicos

### Infraestrutura
- **PostgreSQL**: Banco principal
- **Redis**: Cache e memória
- **Docker**: Containerização
- **Nginx**: Proxy reverso
- **Prometheus**: Monitoramento

## Padrões de Design

### 1. Strategy Pattern
Usado no roteamento de LLMs baseado na complexidade da tarefa.

### 2. Factory Pattern
Criação de clientes LLM e agentes especializados.

### 3. Observer Pattern
Sistema de eventos para monitoramento e logging.

### 4. Command Pattern
Processamento de consultas como comandos executáveis.

## Segurança

### 1. Autenticação e Autorização
- JWT tokens para sessões
- Rate limiting por usuário
- Validação de entrada rigorosa

### 2. Proteção de APIs
- Chaves de API em variáveis de ambiente
- Rotação automática de tokens
- Monitoramento de uso

### 3. Dados Sensíveis
- Criptografia de dados em repouso
- Logs sanitizados
- Conformidade LGPD/GDPR

## Escalabilidade

### 1. Horizontal
- Múltiplas instâncias da API
- Load balancing
- Cache distribuído

### 2. Vertical
- Pool de conexões otimizado
- Async/await em toda aplicação
- Streaming de respostas

### 3. Cache Strategy
- Cache de respostas por TTL
- Cache de dados de mercado
- Invalidação inteligente

## Monitoramento

### 1. Métricas
- Latência de respostas
- Taxa de sucesso por agente
- Uso de tokens por LLM
- Custos por consulta

### 2. Logs
- Logs estruturados (JSON)
- Correlação por session_id
- Alertas automáticos

### 3. Health Checks
- Verificação de LLMs
- Status de APIs externas
- Saúde do banco de dados

## Desenvolvimento

### 1. Ambiente Local
```bash
# Instalar dependências
poetry install

# Configurar ambiente
cp .env.example .env

# Executar migrations
poetry run alembic upgrade head

# Iniciar servidor
poetry run uvicorn src.main:app --reload
```

### 2. Testes
```bash
# Executar todos os testes
poetry run pytest

# Testes com cobertura
poetry run pytest --cov=src

# Testes específicos
poetry run pytest tests/test_agents.py
```

### 3. Deploy
```bash
# Build da imagem
docker build -t chat-ia-agno .

# Deploy com docker-compose
docker-compose up -d
```

## Roadmap

### Fase 1: Base ✅
- [x] Estrutura do projeto
- [x] Integração LLMs
- [x] MCP Memory
- [x] Agente Orquestrador

### Fase 2: Agentes Especializados 🚧
- [ ] Agente de Ações Americanas
- [ ] Agente de Ações Brasileiras
- [ ] Agente de Criptomoedas

### Fase 3: Avançado 📋
- [ ] Interface web
- [ ] Análise de sentimento
- [ ] Alertas automáticos
- [ ] Portfolio tracking
