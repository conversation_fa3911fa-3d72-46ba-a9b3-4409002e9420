[tool.poetry]
name = "chat-ia-agno-financeiro"
version = "0.1.0"
description = "Sistema de Chat IA Financeiro com Arquitetura de Agentes Agno"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
pydantic = "^2.5.0"
httpx = "^0.25.2"
aiohttp = "^3.9.1"
pandas = "^2.1.4"
numpy = "^1.25.2"
plotly = "^5.17.0"
python-dotenv = "^1.0.0"
loguru = "^0.7.2"
redis = "^5.0.1"
sqlalchemy = "^2.0.23"
alembic = "^1.13.1"
psycopg2-binary = "^2.9.9"
openai = "^1.3.7"
anthropic = "^0.7.7"
google-generativeai = "^0.3.2"
langchain = "^0.0.350"
langchain-openai = "^0.0.2"
langchain-anthropic = "^0.0.1"
streamlit = "^1.28.2"
websockets = "^12.0"
beautifulsoup4 = "^4.12.2"
feedparser = "^6.0.10"
yfinance = "^0.2.28"
ccxt = "^4.1.77"
ta = "^0.10.2"
scikit-learn = "^1.3.2"
nltk = "^3.8.1"
spacy = "^3.7.2"
transformers = "^4.36.2"
torch = "^2.1.2"
sentence-transformers = "^2.2.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
