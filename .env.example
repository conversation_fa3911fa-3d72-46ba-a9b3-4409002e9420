# =============================================================================
# CONFIGURAÇÃO DO SISTEMA DE CHAT IA AGNO FINANCEIRO
# =============================================================================

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES GERAIS
# -----------------------------------------------------------------------------
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# -----------------------------------------------------------------------------
# MODELOS DE LINGUAGEM (LLMs)
# -----------------------------------------------------------------------------
# OpenAI
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-1106-preview
OPENAI_MAX_TOKENS=4096

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4096

# Google Gemini
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-pro
GOOGLE_MAX_TOKENS=4096

# -----------------------------------------------------------------------------
# DADOS FINANCEIROS - AÇÕES AMERICANAS
# -----------------------------------------------------------------------------
# Finnhub
FINNHUB_API_KEY=your_finnhub_api_key_here
FINNHUB_BASE_URL=https://finnhub.io/api/v1

# Alpha Vantage
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
ALPHA_VANTAGE_BASE_URL=https://www.alphavantage.co/query

# Federal Reserve Economic Data (FRED)
FRED_API_KEY=your_fred_api_key_here
FRED_BASE_URL=https://api.stlouisfed.org/fred

# SEC EDGAR
SEC_EDGAR_BASE_URL=https://data.sec.gov
SEC_USER_AGENT=YourCompany <EMAIL>

# News API
NEWS_API_KEY=your_news_api_key_here
NEWS_API_BASE_URL=https://newsapi.org/v2

# -----------------------------------------------------------------------------
# DADOS FINANCEIROS - AÇÕES BRASILEIRAS
# -----------------------------------------------------------------------------
# B3 (Brasil Bolsa Balcão)
B3_API_KEY=your_b3_api_key_here
B3_BASE_URL=https://api.b3.com.br

# CVM (Comissão de Valores Mobiliários)
CVM_BASE_URL=https://dados.cvm.gov.br/dados
CVM_USER_AGENT=ChatIAAgno <EMAIL>

# Banco Central do Brasil
BCB_BASE_URL=https://api.bcb.gov.br/dados/serie/bcdata.sgs

# IBGE (Instituto Brasileiro de Geografia e Estatística)
IBGE_BASE_URL=https://servicodados.ibge.gov.br/api/v3

# Cedro Technologies (opcional)
CEDRO_API_KEY=your_cedro_api_key_here
CEDRO_BASE_URL=https://api.cedrotech.com

# Market Data Cloud (opcional)
MARKET_DATA_CLOUD_API_KEY=your_market_data_cloud_key_here
MARKET_DATA_CLOUD_BASE_URL=https://api.marketdatacloud.com

# -----------------------------------------------------------------------------
# DADOS FINANCEIROS - CRIPTOMOEDAS
# -----------------------------------------------------------------------------
# CoinGecko
COINGECKO_API_KEY=your_coingecko_api_key_here
COINGECKO_BASE_URL=https://api.coingecko.com/api/v3

# CoinMarketCap
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here
COINMARKETCAP_BASE_URL=https://pro-api.coinmarketcap.com/v1

# Binance
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_BASE_URL=https://api.binance.com

# Coinbase
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here
COINBASE_BASE_URL=https://api.coinbase.com/v2

# -----------------------------------------------------------------------------
# BANCO DE DADOS
# -----------------------------------------------------------------------------
# PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/chat_ia_agno
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis (Cache e Sessões)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# -----------------------------------------------------------------------------
# SISTEMA MCP MEMORY
# -----------------------------------------------------------------------------
MCP_MEMORY_ENABLED=true
MCP_MEMORY_PROVIDER=redis
MCP_MEMORY_TTL=3600
MCP_MEMORY_MAX_SIZE=1000

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE SEGURANÇA
# -----------------------------------------------------------------------------
SECRET_KEY=your_super_secret_key_here_change_in_production
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:8000
CORS_ALLOW_CREDENTIALS=true

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DO SERVIDOR
# -----------------------------------------------------------------------------
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=true

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE LOGGING
# -----------------------------------------------------------------------------
LOG_FORMAT=json
LOG_FILE=logs/chat_ia_agno.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE RATE LIMITING
# -----------------------------------------------------------------------------
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE MONITORAMENTO
# -----------------------------------------------------------------------------
MONITORING_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE CACHE
# -----------------------------------------------------------------------------
CACHE_ENABLED=true
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE NOTIFICAÇÕES
# -----------------------------------------------------------------------------
# Email (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

# Webhook
WEBHOOK_URL=https://your-webhook-url.com/notifications
WEBHOOK_SECRET=your_webhook_secret

# -----------------------------------------------------------------------------
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# -----------------------------------------------------------------------------
# Apenas para desenvolvimento
DEV_MODE=true
DEV_MOCK_APIS=false
DEV_SAMPLE_DATA=true
