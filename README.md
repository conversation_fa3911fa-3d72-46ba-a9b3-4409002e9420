# Chat IA Agno Financeiro

Sistema de chat com Inteligência Artificial financeiro avançado utilizando uma arquitetura de "Agentes Agno" especializados.

## 🎯 Objetivo

Desenvolver um sistema de chat IA que analise e forneça insights sobre três classes de ativos:
- **Criptomoedas** - Análise técnica e fundamentalista de crypto
- **Ações Americanas** - Mercado dos EUA com dados SEC/EDGAR
- **Ações Brasileiras** - B3/CVM com PLN em português

## 🏗️ Arquitetura

### Motor de IA Híbrido
- Integração sinérgica de múltiplos LLMs (GPT-4.1 Mini, Gemini 2.5 Pro, Gemini 2.5 Flash)
- Sistema de roteamento inteligente baseado em complexidade da tarefa
- Otimização de custos automática

### Arquitetura de Agentes Agno
- **Agente de Orquestração (Meta-Agente)**: Despachante central com NLU/PLN avançado
- **Agente de Ações Americanas**: Especialista em mercado dos EUA
- **Agente de Ações Brasileiras**: Especialista em B3/CVM com PLN em português
- **Agente de Criptomoedas**: Especialista em análise de crypto

### Infraestrutura de Dados

#### Ações Americanas
- APIs: Finnhub, Alpha Vantage
- Fundamentalista: SEC EDGAR API
- Notícias: Finnhub, EODHD, NewsAPI
- Indicadores: FRED (Federal Reserve Economic Data)

#### Ações Brasileiras
- Mercado: Cedro Technologies, Market Data Cloud
- Fundamentalista: Portal CVM, Investidor10
- Notícias: Valor Econômico, InfoMoney
- Indicadores: BCB e IBGE APIs

#### Criptomoedas
- Múltiplas exchanges via CCXT
- Análise on-chain
- Sentiment analysis de redes sociais

## 🚀 Instalação

```bash
# Clone o repositório
git clone <repo-url>
cd chat-ia-agno-financeiro

# Instale as dependências
poetry install

# Configure as variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas chaves de API

# Execute as migrações do banco
poetry run alembic upgrade head

# Inicie o servidor
poetry run uvicorn src.main:app --reload
```

## 📁 Estrutura do Projeto

```
src/
├── agents/                 # Agentes especializados
│   ├── orchestrator/      # Meta-agente orquestrador
│   ├── us_stocks/         # Agente de ações americanas
│   ├── br_stocks/         # Agente de ações brasileiras
│   └── crypto/            # Agente de criptomoedas
├── core/                  # Funcionalidades centrais
│   ├── llm/              # Integração com LLMs
│   ├── memory/           # Sistema MCP Memory
│   └── config/           # Configurações
├── data/                 # Provedores de dados
│   ├── us_market/        # APIs mercado americano
│   ├── br_market/        # APIs mercado brasileiro
│   └── crypto_market/    # APIs criptomoedas
├── api/                  # Endpoints da API
├── chat/                 # Interface de chat
└── utils/                # Utilitários
```

## 🔧 Configuração

### Variáveis de Ambiente

```env
# LLMs
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key

# Dados Financeiros
FINNHUB_API_KEY=your_finnhub_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FRED_API_KEY=your_fred_key

# Banco de Dados
DATABASE_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379

# Configurações
LOG_LEVEL=INFO
ENVIRONMENT=development
```

## 🧪 Testes

```bash
# Execute todos os testes
poetry run pytest

# Testes com cobertura
poetry run pytest --cov=src

# Testes específicos
poetry run pytest tests/agents/
```

## 📊 Funcionalidades

- ✅ NLU avançado com suporte português/inglês
- ✅ Reconhecimento de entidades (tickers, empresas, indicadores)
- ✅ Análise comparativa multi-ativos
- ✅ Geração de gráficos e relatórios no chat
- ✅ Cálculos financeiros sob demanda
- ✅ Memória conversacional persistente (MCP Memory)
- ✅ Sistema de alertas e notificações

## 🔄 Roadmap

### Fase 1: Configuração Base ✅
- [x] Estrutura do projeto
- [x] Integração MCP Memory
- [x] Agente de Ações Americanas

### Fase 2: Expansão Brasileira 🚧
- [ ] Integração B3/CVM
- [ ] Agente de Ações Brasileiras
- [ ] PLN português especializado

### Fase 3: Orquestração e Refinamento 📋
- [ ] Meta-agente completo
- [ ] Agente de criptomoedas refinado
- [ ] Interface de visualização
- [ ] Otimização de performance

## 📄 Licença

MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte

Para suporte, abra uma issue no GitHub ou entre em contato via email.
