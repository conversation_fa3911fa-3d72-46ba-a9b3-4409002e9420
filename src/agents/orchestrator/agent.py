"""
Agente Orquestrador - Meta-agente para roteamento inteligente de consultas.
"""

import re
from typing import Dict, List, Optional, Any
from loguru import logger

from src.agents.base import BaseAgent, AgentQuery, AgentResponse, AgentCapability, AgentStatus
from src.core.llm.base import LLMMessage, TaskComplexity
from src.core.llm.manager import LLMManager


class OrchestratorAgent(BaseAgent):
    """
    Agente Orquestrador - Meta-agente responsável por:
    1. Analisar consultas dos usuários
    2. Determinar qual agente especializado deve responder
    3. Coordenar respostas de múltiplos agentes quando necessário
    4. Processar linguagem natural em português e inglês
    """
    
    def __init__(self, llm_manager: LLMManager):
        super().__init__(
            name="Agente Orquestrador",
            description="Meta-agente responsável por roteamento inteligente de consultas",
            capabilities=[
                AgentCapability.SENTIMENT_ANALYSIS,
                AgentCapability.NEWS_ANALYSIS,
                AgentCapability.MARKET_COMPARISON,
            ],
            llm_manager=llm_manager
        )
        
        # Registro de agentes especializados
        self.specialized_agents: Dict[str, BaseAgent] = {}
        
        # Padrões para classificação de consultas
        self.asset_patterns = {
            'crypto': [
                r'\b(bitcoin|btc|ethereum|eth|crypto|criptomoeda|altcoin)\b',
                r'\b[A-Z]{3,4}USD\b',  # BTCUSD, ETHUSD
                r'\b(binance|coinbase|exchange)\b'
            ],
            'us_stocks': [
                r'\b(aapl|msft|googl|amzn|tsla|nvda|meta)\b',
                r'\b(nyse|nasdaq|sp500|dow jones)\b',
                r'\b(ações? americanas?|us stocks?|american stocks?)\b'
            ],
            'br_stocks': [
                r'\b(petr4|vale3|itub4|bbdc4|abev3|b3sa3)\b',
                r'\b(b3|bovespa|ibovespa)\b',
                r'\b(ações? brasileiras?|br stocks?|brazilian stocks?)\b'
            ]
        }
    
    async def initialize(self) -> None:
        """Inicializa o agente orquestrador."""
        try:
            logger.info("🎯 Inicializando Agente Orquestrador...")
            
            # Verificar se o LLM Manager está disponível
            if not self.llm_manager._initialized:
                await self.llm_manager.initialize()
            
            self.status = AgentStatus.ACTIVE
            self._initialized = True
            
            logger.info("✅ Agente Orquestrador inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Orquestrador: {e}")
            self.status = AgentStatus.ERROR
            raise
    
    def register_agent(self, agent_key: str, agent: BaseAgent) -> None:
        """Registra um agente especializado."""
        self.specialized_agents[agent_key] = agent
        logger.info(f"📝 Agente registrado: {agent_key} - {agent.name}")
    
    async def process_query(self, query: AgentQuery) -> AgentResponse:
        """
        Processa uma consulta determinando o melhor agente para responder.
        
        Args:
            query: Consulta do usuário
            
        Returns:
            Resposta processada
        """
        try:
            # Analisar a consulta
            analysis = await self._analyze_query(query.query, query.context)
            
            # Determinar agente apropriado
            target_agent = self._select_agent(analysis)
            
            # Se há um agente especializado disponível, delegar
            if target_agent and target_agent in self.specialized_agents:
                specialized_agent = self.specialized_agents[target_agent]
                if specialized_agent.is_available():
                    logger.info(f"🔄 Delegando para {target_agent}")
                    return await specialized_agent.process_query(query)
            
            # Caso contrário, processar diretamente
            response_text = await self._process_directly(query, analysis)
            
            return AgentResponse(
                agent_name=self.name,
                response=response_text,
                data={
                    "analysis": analysis,
                    "target_agent": target_agent,
                    "routing_decision": "direct_processing"
                },
                confidence=0.8,
                metadata={
                    "query_type": analysis.get("query_type"),
                    "asset_class": analysis.get("asset_class"),
                    "complexity": analysis.get("complexity")
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Erro no processamento da consulta: {e}")
            return AgentResponse(
                agent_name=self.name,
                response=f"Desculpe, ocorreu um erro ao processar sua consulta: {str(e)}",
                confidence=0.0
            )
    
    async def _analyze_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analisa uma consulta para determinar intenção e contexto.
        
        Args:
            query: Consulta do usuário
            context: Contexto adicional
            
        Returns:
            Análise da consulta
        """
        # Classificar classe de ativo
        asset_class = self._classify_asset_class(query)
        
        # Classificar tipo de consulta
        query_type = self._classify_query_type(query)
        
        # Extrair símbolos mencionados
        symbols = self._extract_symbols(query)
        
        # Determinar complexidade
        complexity = self._assess_complexity(query)
        
        # Detectar idioma
        language = self._detect_language(query)
        
        return {
            "asset_class": asset_class,
            "query_type": query_type,
            "symbols": symbols,
            "complexity": complexity,
            "language": language,
            "original_query": query,
            "context": context
        }
    
    def _classify_asset_class(self, query: str) -> Optional[str]:
        """Classifica a classe de ativo baseada na consulta."""
        query_lower = query.lower()
        
        for asset_class, patterns in self.asset_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    return asset_class
        
        return None
    
    def _classify_query_type(self, query: str) -> str:
        """Classifica o tipo de consulta."""
        query_lower = query.lower()
        
        # Mapeamento de palavras-chave para tipos
        type_keywords = {
            'price': ['preço', 'cotação', 'valor', 'price', 'quote', 'quanto custa'],
            'analysis': ['análise', 'analysis', 'avaliar', 'evaluate', 'como está'],
            'comparison': ['comparar', 'compare', 'vs', 'versus', 'melhor', 'pior'],
            'news': ['notícias', 'news', 'novidades', 'acontecendo'],
            'prediction': ['previsão', 'prediction', 'forecast', 'futuro', 'vai subir', 'vai cair'],
            'portfolio': ['carteira', 'portfolio', 'investir', 'comprar', 'vender'],
            'explanation': ['o que é', 'what is', 'como funciona', 'explain', 'explicar']
        }
        
        for query_type, keywords in type_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return query_type
        
        return 'general'
    
    def _extract_symbols(self, query: str) -> List[str]:
        """Extrai símbolos de ativos da consulta."""
        symbols = []
        
        # Padrões para diferentes tipos de símbolos
        patterns = [
            r'\b[A-Z]{1,5}[0-9]?\b',  # Ações brasileiras (PETR4, VALE3)
            r'\b[A-Z]{2,5}\b',        # Ações americanas (AAPL, MSFT)
            r'\b[A-Z]{3,4}USD\b',     # Pares crypto (BTCUSD)
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, query.upper())
            symbols.extend(matches)
        
        return list(set(symbols))
    
    def _assess_complexity(self, query: str) -> str:
        """Avalia a complexidade da consulta."""
        query_lower = query.lower()
        
        # Indicadores de alta complexidade
        complex_indicators = [
            'análise completa', 'relatório detalhado', 'comparação múltipla',
            'estratégia', 'portfolio', 'diversificação', 'risco'
        ]
        
        # Indicadores de média complexidade
        medium_indicators = [
            'análise', 'comparar', 'avaliar', 'tendência'
        ]
        
        if any(indicator in query_lower for indicator in complex_indicators):
            return 'complex'
        elif any(indicator in query_lower for indicator in medium_indicators):
            return 'medium'
        else:
            return 'simple'
    
    def _detect_language(self, query: str) -> str:
        """Detecta o idioma da consulta."""
        # Palavras indicativas de português
        pt_words = ['preço', 'ação', 'análise', 'cotação', 'mercado', 'investir']
        
        # Palavras indicativas de inglês
        en_words = ['price', 'stock', 'analysis', 'market', 'invest', 'trading']
        
        query_lower = query.lower()
        
        pt_count = sum(1 for word in pt_words if word in query_lower)
        en_count = sum(1 for word in en_words if word in query_lower)
        
        if pt_count > en_count:
            return 'pt'
        elif en_count > pt_count:
            return 'en'
        else:
            return 'auto'  # Detectar automaticamente
    
    def _select_agent(self, analysis: Dict[str, Any]) -> Optional[str]:
        """Seleciona o agente mais apropriado baseado na análise."""
        asset_class = analysis.get("asset_class")
        
        # Mapeamento de classes de ativo para agentes
        agent_mapping = {
            'crypto': 'crypto_agent',
            'us_stocks': 'us_stocks_agent',
            'br_stocks': 'br_stocks_agent'
        }
        
        return agent_mapping.get(asset_class)
    
    async def _process_directly(self, query: AgentQuery, analysis: Dict[str, Any]) -> str:
        """Processa a consulta diretamente quando não há agente especializado."""
        messages = [
            LLMMessage(role="system", content=self.get_system_prompt()),
            LLMMessage(role="user", content=f"""
Análise da consulta:
- Classe de ativo: {analysis.get('asset_class', 'não identificada')}
- Tipo: {analysis.get('query_type', 'geral')}
- Símbolos: {', '.join(analysis.get('symbols', []))}
- Complexidade: {analysis.get('complexity', 'média')}

Consulta do usuário: {query.query}

Por favor, forneça uma resposta útil baseada na análise acima.
""")
        ]
        
        complexity_map = {
            'simple': TaskComplexity.SIMPLE,
            'medium': TaskComplexity.MEDIUM,
            'complex': TaskComplexity.COMPLEX
        }
        
        complexity = complexity_map.get(analysis.get('complexity', 'medium'), TaskComplexity.MEDIUM)
        
        return await self._generate_llm_response(messages, complexity)
    
    def get_system_prompt(self) -> str:
        """Retorna o prompt de sistema do orquestrador."""
        return """Você é o Agente Orquestrador do Chat IA Agno Financeiro, um meta-agente especializado em análise financeira.

Suas responsabilidades:
1. Analisar consultas dos usuários sobre investimentos
2. Fornecer respostas precisas sobre criptomoedas, ações americanas e brasileiras
3. Coordenar informações de múltiplas fontes quando necessário
4. Processar consultas em português e inglês

Capacidades:
- Análise de mercado em tempo real
- Comparação entre diferentes classes de ativos
- Interpretação de indicadores técnicos e fundamentalistas
- Análise de sentimento e notícias

Diretrizes:
- Sempre forneça informações precisas e atualizadas
- Inclua avisos sobre riscos quando apropriado
- Cite fontes quando possível
- Mantenha um tom profissional mas acessível
- Adapte a complexidade da resposta ao nível da pergunta

Importante: Suas análises são para fins educacionais e informativos, não constituindo recomendações de investimento."""
    
    def can_handle(self, query: str, context: Optional[Dict[str, Any]] = None) -> float:
        """O orquestrador pode lidar com qualquer consulta."""
        return 1.0  # Sempre pode processar como fallback
