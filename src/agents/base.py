"""
Classes base para agentes especializados.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from src.core.llm.base import LLMMessage, TaskComplexity
from src.core.llm.manager import LLMManager


class AgentStatus(Enum):
    """Status dos agentes."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEVELOPMENT = "development"
    ERROR = "error"


class AgentCapability(Enum):
    """Capacidades dos agentes."""
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    NEWS_ANALYSIS = "news_analysis"
    PRICE_PREDICTION = "price_prediction"
    RISK_ASSESSMENT = "risk_assessment"
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    MARKET_COMPARISON = "market_comparison"


@dataclass
class AgentQuery:
    """Consulta para um agente."""
    user_id: str
    session_id: str
    query: str
    parameters: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class AgentResponse:
    """Resposta de um agente."""
    agent_name: str
    response: str
    data: Optional[Dict[str, Any]] = None
    sources: Optional[List[str]] = None
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class BaseAgent(ABC):
    """Classe base para todos os agentes especializados."""
    
    def __init__(
        self,
        name: str,
        description: str,
        capabilities: List[AgentCapability],
        llm_manager: LLMManager
    ):
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.llm_manager = llm_manager
        self.status = AgentStatus.INACTIVE
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Inicializa o agente."""
        pass
    
    @abstractmethod
    async def process_query(self, query: AgentQuery) -> AgentResponse:
        """Processa uma consulta."""
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Retorna o prompt de sistema específico do agente."""
        pass
    
    async def _generate_llm_response(
        self,
        messages: List[LLMMessage],
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        **kwargs
    ) -> str:
        """
        Gera resposta usando o LLM.
        
        Args:
            messages: Mensagens para o LLM
            complexity: Complexidade da tarefa
            **kwargs: Argumentos adicionais
            
        Returns:
            Resposta do LLM
        """
        response = await self.llm_manager.generate_response(
            messages=messages,
            complexity=complexity,
            **kwargs
        )
        return response.content
    
    def can_handle(self, query: str, context: Optional[Dict[str, Any]] = None) -> float:
        """
        Determina se o agente pode lidar com a consulta.
        
        Args:
            query: Consulta do usuário
            context: Contexto adicional
            
        Returns:
            Score de confiança (0.0 a 1.0)
        """
        # Implementação básica - subclasses devem sobrescrever
        return 0.5
    
    def get_capabilities(self) -> List[str]:
        """Retorna lista de capacidades do agente."""
        return [cap.value for cap in self.capabilities]
    
    def get_status(self) -> str:
        """Retorna status atual do agente."""
        return self.status.value
    
    def is_available(self) -> bool:
        """Verifica se o agente está disponível."""
        return self.status == AgentStatus.ACTIVE and self._initialized
    
    async def health_check(self) -> bool:
        """
        Verifica a saúde do agente.
        
        Returns:
            True se o agente está saudável
        """
        try:
            if not self.is_available():
                return False
            
            # Teste básico com o LLM
            test_messages = [
                LLMMessage(role="system", content=self.get_system_prompt()),
                LLMMessage(role="user", content="Health check - respond with 'OK'")
            ]
            
            response = await self._generate_llm_response(
                messages=test_messages,
                complexity=TaskComplexity.SIMPLE,
                max_tokens=10
            )
            
            return "OK" in response.upper()
            
        except Exception:
            return False


class FinancialAgent(BaseAgent):
    """Classe base para agentes financeiros."""
    
    def __init__(
        self,
        name: str,
        description: str,
        capabilities: List[AgentCapability],
        llm_manager: LLMManager,
        asset_classes: List[str]
    ):
        super().__init__(name, description, capabilities, llm_manager)
        self.asset_classes = asset_classes
    
    def supports_asset_class(self, asset_class: str) -> bool:
        """Verifica se o agente suporta uma classe de ativo."""
        return asset_class in self.asset_classes
    
    @abstractmethod
    async def get_market_data(self, symbol: str, **kwargs) -> Dict[str, Any]:
        """Obtém dados de mercado para um símbolo."""
        pass
    
    @abstractmethod
    async def analyze_asset(self, symbol: str, analysis_type: str) -> Dict[str, Any]:
        """Analisa um ativo específico."""
        pass
    
    def _extract_symbols(self, query: str) -> List[str]:
        """
        Extrai símbolos de ativos da consulta.
        
        Args:
            query: Consulta do usuário
            
        Returns:
            Lista de símbolos encontrados
        """
        # Implementação básica - pode ser melhorada com NLP
        import re
        
        # Padrões comuns para símbolos
        patterns = [
            r'\b[A-Z]{1,5}\b',  # Símbolos de ações (ex: AAPL, PETR4)
            r'\b[A-Z]{2,4}USD\b',  # Pares crypto (ex: BTCUSD)
            r'\b[A-Z]{3,4}/[A-Z]{3,4}\b',  # Pares com barra (ex: BTC/USD)
        ]
        
        symbols = []
        for pattern in patterns:
            matches = re.findall(pattern, query.upper())
            symbols.extend(matches)
        
        return list(set(symbols))  # Remove duplicatas
    
    def _classify_query_type(self, query: str) -> str:
        """
        Classifica o tipo de consulta.
        
        Args:
            query: Consulta do usuário
            
        Returns:
            Tipo da consulta
        """
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['preço', 'cotação', 'valor', 'price', 'quote']):
            return 'price_query'
        elif any(word in query_lower for word in ['análise', 'analysis', 'avaliar', 'evaluate']):
            return 'analysis_query'
        elif any(word in query_lower for word in ['comparar', 'compare', 'vs', 'versus']):
            return 'comparison_query'
        elif any(word in query_lower for word in ['notícias', 'news', 'novidades']):
            return 'news_query'
        elif any(word in query_lower for word in ['previsão', 'prediction', 'forecast']):
            return 'prediction_query'
        else:
            return 'general_query'
