"""
Modelos Pydantic para a API.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field, validator


class MessageRole(str, Enum):
    """Roles de mensagens no chat."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class TaskComplexityEnum(str, Enum):
    """Níveis de complexidade de tarefas."""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"


class LLMProviderEnum(str, Enum):
    """Provedores de LLM."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"


class AssetClass(str, Enum):
    """Classes de ativos suportadas."""
    CRYPTO = "crypto"
    US_STOCKS = "us_stocks"
    BR_STOCKS = "br_stocks"


# Modelos de Chat
class ChatMessage(BaseModel):
    """Mensagem no chat."""
    role: MessageRole
    content: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ChatRequest(BaseModel):
    """Requisição de chat."""
    message: str = Field(..., min_length=1, max_length=10000)
    session_id: Optional[str] = None
    user_id: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = None
    complexity: Optional[TaskComplexityEnum] = TaskComplexityEnum.MEDIUM
    provider: Optional[LLMProviderEnum] = None
    asset_class: Optional[AssetClass] = None
    cost_priority: bool = False
    stream: bool = False
    
    @validator('message')
    def validate_message(cls, v):
        if not v.strip():
            raise ValueError('Mensagem não pode estar vazia')
        return v.strip()


class ChatResponse(BaseModel):
    """Resposta do chat."""
    message: str
    session_id: str
    agent_used: str
    provider_used: str
    model_used: str
    tokens_used: int
    cost_estimate: float
    response_time_ms: int
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StreamChatResponse(BaseModel):
    """Resposta de chat em streaming."""
    chunk: str
    session_id: str
    is_final: bool = False
    metadata: Optional[Dict[str, Any]] = None


# Modelos de Agentes
class AgentInfo(BaseModel):
    """Informações sobre um agente."""
    name: str
    description: str
    asset_class: AssetClass
    capabilities: List[str]
    status: str
    last_updated: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AgentRequest(BaseModel):
    """Requisição específica para um agente."""
    query: str = Field(..., min_length=1, max_length=5000)
    parameters: Optional[Dict[str, Any]] = None
    user_id: str = Field(..., min_length=1)
    session_id: Optional[str] = None


class AgentResponse(BaseModel):
    """Resposta de um agente."""
    agent_name: str
    response: str
    data: Optional[Dict[str, Any]] = None
    sources: Optional[List[str]] = None
    confidence: Optional[float] = None
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Modelos de Dados Financeiros
class StockQuote(BaseModel):
    """Cotação de ação."""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    market_cap: Optional[float] = None
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CryptoQuote(BaseModel):
    """Cotação de criptomoeda."""
    symbol: str
    price: float
    change_24h: float
    change_percent_24h: float
    volume_24h: float
    market_cap: Optional[float] = None
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class FinancialAnalysis(BaseModel):
    """Análise financeira."""
    asset_symbol: str
    asset_class: AssetClass
    analysis_type: str
    summary: str
    details: Dict[str, Any]
    recommendations: List[str]
    risk_level: str
    confidence_score: float
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Modelos de Memória
class MemoryEntry(BaseModel):
    """Entrada de memória."""
    id: str
    user_id: str
    session_id: str
    content: Dict[str, Any]
    tags: List[str] = []
    created_at: datetime
    expires_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MemorySearchRequest(BaseModel):
    """Requisição de busca na memória."""
    user_id: str
    session_id: Optional[str] = None
    tags: Optional[List[str]] = None
    limit: int = Field(default=10, ge=1, le=100)


# Modelos de Erro
class ErrorResponse(BaseModel):
    """Resposta de erro."""
    error: str
    detail: Optional[str] = None
    status_code: int
    timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# Modelos de Health Check
class HealthStatus(BaseModel):
    """Status de saúde."""
    status: str
    timestamp: datetime
    service: str
    version: str
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DetailedHealthStatus(HealthStatus):
    """Status de saúde detalhado."""
    components: Dict[str, Union[bool, Dict[str, bool]]]
    available_providers: List[str]
