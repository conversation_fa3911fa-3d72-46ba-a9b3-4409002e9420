"""
Endpoints de health check e monitoramento.
"""

from fastapi import APIRouter, Depends
from typing import Dict, Any
from datetime import datetime

from src.core.llm.manager import get_llm_manager, LLMManager
from src.core.database import DatabaseManager

router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Health check básico da aplicação."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Chat IA Agno Financeiro",
        "version": "0.1.0"
    }


@router.get("/detailed")
async def detailed_health_check(
    llm_manager: LLMManager = Depends(get_llm_manager)
) -> Dict[str, Any]:
    """Health check detalhado de todos os componentes."""
    
    # Verificar LLMs
    llm_status = await llm_manager.health_check()
    
    # Verificar banco de dados
    db_manager = DatabaseManager()
    try:
        await db_manager.initialize()
        db_status = await db_manager.health_check()
        await db_manager.close()
    except Exception:
        db_status = False
    
    # Status geral
    all_healthy = (
        all(llm_status.values()) and
        db_status
    )
    
    return {
        "status": "healthy" if all_healthy else "degraded",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {
            "database": db_status,
            "llms": llm_status,
        },
        "available_providers": list(llm_status.keys()),
        "service": "Chat IA Agno Financeiro",
        "version": "0.1.0"
    }


@router.get("/llms")
async def llm_health_check(
    llm_manager: LLMManager = Depends(get_llm_manager)
) -> Dict[str, Any]:
    """Health check específico dos LLMs."""
    
    llm_status = await llm_manager.health_check()
    available_providers = llm_manager.get_available_providers()
    
    return {
        "status": "healthy" if all(llm_status.values()) else "degraded",
        "timestamp": datetime.utcnow().isoformat(),
        "providers": llm_status,
        "available_providers": [p.value for p in available_providers],
        "total_providers": len(available_providers)
    }
