"""
Endpoints de chat principal.
"""

import time
import uuid
from datetime import datetime
from typing import AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from loguru import logger

from src.api.models import (
    ChatRequest, ChatResponse, StreamChatResponse, 
    TaskComplexityEnum, LLMProviderEnum
)
from src.core.llm.manager import get_llm_manager, LLMManager
from src.core.llm.base import LLMMessage, TaskComplexity, LLMProvider
from src.core.memory.mcp_memory import MCPMemoryManager

router = APIRouter()


def convert_complexity(complexity: TaskComplexityEnum) -> TaskComplexity:
    """Converte enum da API para enum interno."""
    mapping = {
        TaskComplexityEnum.SIMPLE: TaskComplexity.SIMPLE,
        TaskComplexityEnum.MEDIUM: TaskComplexity.MEDIUM,
        TaskComplexityEnum.COMPLEX: TaskComplexity.COMPLEX,
    }
    return mapping[complexity]


def convert_provider(provider: LLMProviderEnum) -> LLMProvider:
    """Converte enum da API para enum interno."""
    mapping = {
        LLMProviderEnum.OPENAI: LLMProvider.OPENAI,
        LLMProviderEnum.ANTHROPIC: LLMProvider.ANTHROPIC,
        LLMProviderEnum.GOOGLE: LLMProvider.GOOGLE,
    }
    return mapping[provider]


@router.post("/", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    llm_manager: LLMManager = Depends(get_llm_manager)
) -> ChatResponse:
    """
    Endpoint principal de chat.
    
    Processa uma mensagem do usuário e retorna a resposta do sistema.
    """
    start_time = time.time()
    
    try:
        # Gerar session_id se não fornecido
        session_id = request.session_id or str(uuid.uuid4())
        
        # Preparar mensagens para o LLM
        messages = [
            LLMMessage(
                role="system",
                content=_get_system_prompt(request.asset_class)
            ),
            LLMMessage(
                role="user",
                content=request.message
            )
        ]
        
        # TODO: Adicionar contexto da memória conversacional
        # TODO: Implementar roteamento para agentes especializados
        
        # Converter parâmetros
        complexity = convert_complexity(request.complexity)
        provider = convert_provider(request.provider) if request.provider else None
        
        # Gerar resposta
        response = await llm_manager.generate_response(
            messages=messages,
            complexity=complexity,
            provider=provider,
            cost_priority=request.cost_priority
        )
        
        # Calcular tempo de resposta
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # TODO: Salvar na memória conversacional
        
        return ChatResponse(
            message=response.content,
            session_id=session_id,
            agent_used="orchestrator",  # TODO: Implementar roteamento de agentes
            provider_used=response.provider.value,
            model_used=response.model,
            tokens_used=response.tokens_used,
            cost_estimate=response.cost_estimate,
            response_time_ms=response_time_ms,
            timestamp=datetime.utcnow(),
            metadata=response.metadata
        )
        
    except Exception as e:
        logger.error(f"❌ Erro no chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.post("/stream")
async def chat_stream(
    request: ChatRequest,
    llm_manager: LLMManager = Depends(get_llm_manager)
):
    """
    Endpoint de chat com streaming.
    
    Retorna a resposta em chunks para uma experiência mais fluida.
    """
    try:
        # Gerar session_id se não fornecido
        session_id = request.session_id or str(uuid.uuid4())
        
        # Preparar mensagens para o LLM
        messages = [
            LLMMessage(
                role="system",
                content=_get_system_prompt(request.asset_class)
            ),
            LLMMessage(
                role="user",
                content=request.message
            )
        ]
        
        # Converter parâmetros
        complexity = convert_complexity(request.complexity)
        provider = convert_provider(request.provider) if request.provider else None
        
        async def generate_stream():
            """Gerador de stream de resposta."""
            try:
                async for chunk in llm_manager.stream_response(
                    messages=messages,
                    complexity=complexity,
                    provider=provider,
                    cost_priority=request.cost_priority
                ):
                    response_chunk = StreamChatResponse(
                        chunk=chunk,
                        session_id=session_id,
                        is_final=False
                    )
                    yield f"data: {response_chunk.json()}\n\n"
                
                # Chunk final
                final_chunk = StreamChatResponse(
                    chunk="",
                    session_id=session_id,
                    is_final=True
                )
                yield f"data: {final_chunk.json()}\n\n"
                
            except Exception as e:
                logger.error(f"❌ Erro no streaming: {e}")
                error_chunk = StreamChatResponse(
                    chunk=f"Erro: {str(e)}",
                    session_id=session_id,
                    is_final=True
                )
                yield f"data: {error_chunk.json()}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Erro no chat stream: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


def _get_system_prompt(asset_class=None) -> str:
    """
    Retorna o prompt de sistema baseado na classe de ativo.
    
    Args:
        asset_class: Classe de ativo específica
        
    Returns:
        Prompt de sistema
    """
    base_prompt = """Você é um assistente de IA especializado em análise financeira e investimentos.

Você tem acesso a dados em tempo real sobre:
- Criptomoedas
- Ações americanas (NYSE, NASDAQ)
- Ações brasileiras (B3)

Suas capacidades incluem:
- Análise técnica e fundamentalista
- Comparação de ativos
- Cálculos financeiros
- Interpretação de indicadores econômicos
- Análise de sentimento de mercado

Sempre forneça:
1. Respostas precisas e baseadas em dados
2. Contexto e explicações claras
3. Avisos sobre riscos quando apropriado
4. Fontes das informações quando possível

Importante: Suas análises são para fins educacionais e informativos. Não constituem recomendações de investimento."""
    
    if asset_class:
        if asset_class == "crypto":
            base_prompt += "\n\nFoco atual: Análise de criptomoedas e ativos digitais."
        elif asset_class == "us_stocks":
            base_prompt += "\n\nFoco atual: Análise de ações americanas (NYSE, NASDAQ)."
        elif asset_class == "br_stocks":
            base_prompt += "\n\nFoco atual: Análise de ações brasileiras (B3)."
    
    return base_prompt
