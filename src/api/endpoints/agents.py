"""
Endpoints para interação com agentes especializados.
"""

from datetime import datetime
from typing import List
from fastapi import APIRouter, HTTPException
from loguru import logger

from src.api.models import AgentInfo, AgentRequest, AgentResponse, AssetClass

router = APIRouter()


# Informações dos agentes (temporário - será movido para banco de dados)
AGENTS_INFO = {
    "orchestrator": AgentInfo(
        name="Agente Orquestrador",
        description="Meta-agente responsável por rotear consultas para agentes especializados",
        asset_class=AssetClass.CRYPTO,  # Placeholder
        capabilities=[
            "Roteamento inteligente de consultas",
            "Processamento de linguagem natural",
            "Coordenação entre agentes",
            "Análise de contexto"
        ],
        status="active",
        last_updated=datetime.utcnow()
    ),
    "crypto_agent": AgentInfo(
        name="Agente de Criptomoedas",
        description="Especialista em análise de criptomoedas e ativos digitais",
        asset_class=AssetClass.CRYPTO,
        capabilities=[
            "Análise técnica de criptomoedas",
            "Monitoramento de exchanges",
            "Análise on-chain",
            "Sentiment analysis de redes sociais",
            "Comparação de altcoins"
        ],
        status="active",
        last_updated=datetime.utcnow()
    ),
    "us_stocks_agent": AgentInfo(
        name="Agente de Ações Americanas",
        description="Especialista em mercado de ações dos EUA (NYSE, NASDAQ)",
        asset_class=AssetClass.US_STOCKS,
        capabilities=[
            "Análise fundamentalista SEC/EDGAR",
            "Dados de earnings e relatórios",
            "Indicadores econômicos FRED",
            "Análise de setores",
            "Notícias financeiras"
        ],
        status="active",
        last_updated=datetime.utcnow()
    ),
    "br_stocks_agent": AgentInfo(
        name="Agente de Ações Brasileiras",
        description="Especialista em mercado brasileiro (B3) com PLN em português",
        asset_class=AssetClass.BR_STOCKS,
        capabilities=[
            "Dados da B3 e CVM",
            "Análise fundamentalista brasileira",
            "Indicadores econômicos BCB/IBGE",
            "Notícias em português",
            "Análise de setores brasileiros"
        ],
        status="development",  # Em desenvolvimento
        last_updated=datetime.utcnow()
    )
}


@router.get("/", response_model=List[AgentInfo])
async def list_agents() -> List[AgentInfo]:
    """Lista todos os agentes disponíveis."""
    return list(AGENTS_INFO.values())


@router.get("/{agent_name}", response_model=AgentInfo)
async def get_agent_info(agent_name: str) -> AgentInfo:
    """Obtém informações detalhadas sobre um agente específico."""
    if agent_name not in AGENTS_INFO:
        raise HTTPException(
            status_code=404,
            detail=f"Agente '{agent_name}' não encontrado"
        )
    
    return AGENTS_INFO[agent_name]


@router.post("/{agent_name}/query", response_model=AgentResponse)
async def query_agent(
    agent_name: str,
    request: AgentRequest
) -> AgentResponse:
    """
    Faz uma consulta direta a um agente específico.
    
    Este endpoint permite interação direta com agentes especializados,
    bypassando o roteamento automático do orquestrador.
    """
    if agent_name not in AGENTS_INFO:
        raise HTTPException(
            status_code=404,
            detail=f"Agente '{agent_name}' não encontrado"
        )
    
    agent_info = AGENTS_INFO[agent_name]
    
    # Verificar se o agente está ativo
    if agent_info.status != "active":
        raise HTTPException(
            status_code=503,
            detail=f"Agente '{agent_name}' não está disponível (status: {agent_info.status})"
        )
    
    try:
        # TODO: Implementar lógica específica de cada agente
        # Por enquanto, retorna uma resposta placeholder
        
        if agent_name == "orchestrator":
            response_text = await _handle_orchestrator_query(request)
        elif agent_name == "crypto_agent":
            response_text = await _handle_crypto_query(request)
        elif agent_name == "us_stocks_agent":
            response_text = await _handle_us_stocks_query(request)
        elif agent_name == "br_stocks_agent":
            response_text = await _handle_br_stocks_query(request)
        else:
            response_text = f"Agente {agent_name} processou: {request.query}"
        
        return AgentResponse(
            agent_name=agent_info.name,
            response=response_text,
            data={"processed_query": request.query},
            sources=["placeholder"],
            confidence=0.8,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"❌ Erro ao consultar agente {agent_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do agente: {str(e)}"
        )


@router.get("/status/summary")
async def get_agents_status():
    """Retorna um resumo do status de todos os agentes."""
    status_summary = {
        "total_agents": len(AGENTS_INFO),
        "active_agents": len([a for a in AGENTS_INFO.values() if a.status == "active"]),
        "development_agents": len([a for a in AGENTS_INFO.values() if a.status == "development"]),
        "agents_by_asset_class": {}
    }
    
    # Agrupar por classe de ativo
    for agent in AGENTS_INFO.values():
        asset_class = agent.asset_class.value
        if asset_class not in status_summary["agents_by_asset_class"]:
            status_summary["agents_by_asset_class"][asset_class] = []
        
        status_summary["agents_by_asset_class"][asset_class].append({
            "name": agent.name,
            "status": agent.status
        })
    
    return status_summary


# Handlers específicos para cada agente (placeholders)
async def _handle_orchestrator_query(request: AgentRequest) -> str:
    """Handler para o agente orquestrador."""
    return f"Orquestrador analisou: '{request.query}'. Roteamento para agente apropriado seria implementado aqui."


async def _handle_crypto_query(request: AgentRequest) -> str:
    """Handler para o agente de criptomoedas."""
    return f"Agente de Crypto analisou: '{request.query}'. Análise de criptomoedas seria implementada aqui."


async def _handle_us_stocks_query(request: AgentRequest) -> str:
    """Handler para o agente de ações americanas."""
    return f"Agente de Ações US analisou: '{request.query}'. Análise de ações americanas seria implementada aqui."


async def _handle_br_stocks_query(request: AgentRequest) -> str:
    """Handler para o agente de ações brasileiras."""
    return f"Agente de Ações BR analisou: '{request.query}'. Análise de ações brasileiras seria implementada aqui."
