"""
Ponto de entrada principal da aplicação Chat IA Agno Financeiro.
"""

import asyncio
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger

from src.core.config import get_settings
from src.core.logging import setup_logging
from src.api.routes import api_router
from src.core.database import init_db, close_db
from src.core.memory.mcp_memory import MCPMemoryManager


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Gerencia o ciclo de vida da aplicação."""
    logger.info("🚀 Iniciando Chat IA Agno Financeiro...")
    
    # Inicializar banco de dados
    await init_db()
    logger.info("✅ Banco de dados inicializado")
    
    # Inicializar MCP Memory
    memory_manager = MCPMemoryManager()
    await memory_manager.initialize()
    app.state.memory_manager = memory_manager
    logger.info("✅ MCP Memory inicializado")
    
    logger.info("🎯 Sistema pronto para uso!")
    
    yield
    
    # Cleanup
    logger.info("🔄 Finalizando aplicação...")
    await memory_manager.close()
    await close_db()
    logger.info("✅ Aplicação finalizada")


def create_app() -> FastAPI:
    """Cria e configura a aplicação FastAPI."""
    settings = get_settings()
    
    # Configurar logging
    setup_logging(settings.log_level, settings.environment)
    
    # Criar aplicação
    app = FastAPI(
        title="Chat IA Agno Financeiro",
        description="Sistema de chat IA financeiro com arquitetura de agentes especializados",
        version="0.1.0",
        docs_url="/docs" if settings.environment == "development" else None,
        redoc_url="/redoc" if settings.environment == "development" else None,
        lifespan=lifespan,
    )
    
    # Configurar CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Incluir rotas
    app.include_router(api_router, prefix="/api/v1")
    
    # Health check
    @app.get("/health")
    async def health_check():
        """Endpoint de verificação de saúde."""
        return JSONResponse(
            content={
                "status": "healthy",
                "service": "Chat IA Agno Financeiro",
                "version": "0.1.0",
                "environment": settings.environment,
            }
        )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Endpoint raiz."""
        return JSONResponse(
            content={
                "message": "Chat IA Agno Financeiro API",
                "version": "0.1.0",
                "docs": "/docs",
                "health": "/health",
            }
        )
    
    # Exception handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc):
        logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.detail, "status_code": exc.status_code},
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        logger.error(f"Unhandled exception: {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "status_code": 500,
                "detail": str(exc) if settings.environment == "development" else None,
            },
        )
    
    return app


# Criar instância da aplicação
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        workers=1 if settings.reload else settings.workers,
        log_level=settings.log_level.lower(),
    )
