"""
Configurações centrais do sistema Chat IA Agno Financeiro.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Configurações da aplicação."""
    
    # Configurações gerais
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Servidor
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=4, env="WORKERS")
    reload: bool = Field(default=True, env="RELOAD")
    
    # Segurança
    secret_key: str = Field(env="SECRET_KEY")
    jwt_secret_key: str = Field(env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(default=24, env="JWT_EXPIRATION_HOURS")
    
    # CORS
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    
    # Banco de dados
    database_url: str = Field(env="DATABASE_URL")
    database_pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    
    # Redis
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # LLMs - OpenAI
    openai_api_key: str = Field(env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4-1106-preview", env="OPENAI_MODEL")
    openai_max_tokens: int = Field(default=4096, env="OPENAI_MAX_TOKENS")
    
    # LLMs - Anthropic
    anthropic_api_key: str = Field(env="ANTHROPIC_API_KEY")
    anthropic_model: str = Field(default="claude-3-sonnet-20240229", env="ANTHROPIC_MODEL")
    anthropic_max_tokens: int = Field(default=4096, env="ANTHROPIC_MAX_TOKENS")
    
    # LLMs - Google
    google_api_key: str = Field(env="GOOGLE_API_KEY")
    google_model: str = Field(default="gemini-pro", env="GOOGLE_MODEL")
    google_max_tokens: int = Field(default=4096, env="GOOGLE_MAX_TOKENS")
    
    # APIs Financeiras - US Market
    finnhub_api_key: str = Field(env="FINNHUB_API_KEY")
    finnhub_base_url: str = Field(default="https://finnhub.io/api/v1", env="FINNHUB_BASE_URL")
    
    alpha_vantage_api_key: str = Field(env="ALPHA_VANTAGE_API_KEY")
    alpha_vantage_base_url: str = Field(
        default="https://www.alphavantage.co/query", 
        env="ALPHA_VANTAGE_BASE_URL"
    )
    
    fred_api_key: str = Field(env="FRED_API_KEY")
    fred_base_url: str = Field(
        default="https://api.stlouisfed.org/fred", 
        env="FRED_BASE_URL"
    )
    
    sec_edgar_base_url: str = Field(
        default="https://data.sec.gov", 
        env="SEC_EDGAR_BASE_URL"
    )
    sec_user_agent: str = Field(
        default="ChatIAAgno <EMAIL>", 
        env="SEC_USER_AGENT"
    )
    
    news_api_key: str = Field(env="NEWS_API_KEY")
    news_api_base_url: str = Field(
        default="https://newsapi.org/v2", 
        env="NEWS_API_BASE_URL"
    )
    
    # APIs Financeiras - BR Market
    b3_api_key: Optional[str] = Field(default=None, env="B3_API_KEY")
    b3_base_url: str = Field(default="https://api.b3.com.br", env="B3_BASE_URL")
    
    cvm_base_url: str = Field(
        default="https://dados.cvm.gov.br/dados", 
        env="CVM_BASE_URL"
    )
    cvm_user_agent: str = Field(
        default="ChatIAAgno <EMAIL>", 
        env="CVM_USER_AGENT"
    )
    
    bcb_base_url: str = Field(
        default="https://api.bcb.gov.br/dados/serie/bcdata.sgs", 
        env="BCB_BASE_URL"
    )
    
    ibge_base_url: str = Field(
        default="https://servicodados.ibge.gov.br/api/v3", 
        env="IBGE_BASE_URL"
    )
    
    # APIs Financeiras - Crypto
    coingecko_api_key: Optional[str] = Field(default=None, env="COINGECKO_API_KEY")
    coingecko_base_url: str = Field(
        default="https://api.coingecko.com/api/v3", 
        env="COINGECKO_BASE_URL"
    )
    
    coinmarketcap_api_key: str = Field(env="COINMARKETCAP_API_KEY")
    coinmarketcap_base_url: str = Field(
        default="https://pro-api.coinmarketcap.com/v1", 
        env="COINMARKETCAP_BASE_URL"
    )
    
    binance_api_key: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    binance_secret_key: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    binance_base_url: str = Field(
        default="https://api.binance.com", 
        env="BINANCE_BASE_URL"
    )
    
    # MCP Memory
    mcp_memory_enabled: bool = Field(default=True, env="MCP_MEMORY_ENABLED")
    mcp_memory_provider: str = Field(default="redis", env="MCP_MEMORY_PROVIDER")
    mcp_memory_ttl: int = Field(default=3600, env="MCP_MEMORY_TTL")
    mcp_memory_max_size: int = Field(default=1000, env="MCP_MEMORY_MAX_SIZE")
    
    # Cache
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl_seconds: int = Field(default=300, env="CACHE_TTL_SECONDS")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(
        default=60, 
        env="RATE_LIMIT_REQUESTS_PER_MINUTE"
    )
    rate_limit_burst: int = Field(default=10, env="RATE_LIMIT_BURST")
    
    # Monitoramento
    monitoring_enabled: bool = Field(default=True, env="MONITORING_ENABLED")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # Logging
    log_format: str = Field(default="json", env="LOG_FORMAT")
    log_file: str = Field(default="logs/chat_ia_agno.log", env="LOG_FILE")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Retorna as configurações da aplicação (cached)."""
    return Settings()
