"""
Implementação do sistema MCP Memory para persistência de contexto conversacional.
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod

import redis.asyncio as redis
from loguru import logger

from src.core.config import get_settings


@dataclass
class MemoryEntry:
    """Entrada de memória no sistema MCP."""
    
    id: str
    user_id: str
    session_id: str
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário."""
        data = asdict(self)
        # Converter datetime para string
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.expires_at:
            data['expires_at'] = self.expires_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """Cria instância a partir de dicionário."""
        # Converter strings para datetime
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        if data.get('expires_at'):
            data['expires_at'] = datetime.fromisoformat(data['expires_at'])
        return cls(**data)


class MemoryProvider(ABC):
    """Interface abstrata para provedores de memória."""
    
    @abstractmethod
    async def store(self, entry: MemoryEntry) -> bool:
        """Armazena uma entrada de memória."""
        pass
    
    @abstractmethod
    async def retrieve(self, entry_id: str) -> Optional[MemoryEntry]:
        """Recupera uma entrada de memória por ID."""
        pass
    
    @abstractmethod
    async def search(
        self, 
        user_id: str, 
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """Busca entradas de memória."""
        pass
    
    @abstractmethod
    async def delete(self, entry_id: str) -> bool:
        """Remove uma entrada de memória."""
        pass
    
    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Remove entradas expiradas."""
        pass


class RedisMemoryProvider(MemoryProvider):
    """Provedor de memória usando Redis."""
    
    def __init__(self, redis_url: str, password: Optional[str] = None):
        self.redis_url = redis_url
        self.password = password
        self.redis_client: Optional[redis.Redis] = None
    
    async def initialize(self) -> None:
        """Inicializa a conexão com Redis."""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                password=self.password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
            )
            
            # Testar conexão
            await self.redis_client.ping()
            logger.info("✅ Redis Memory Provider inicializado")
            
        except Exception as e:
            logger.error(f"❌ Erro ao conectar com Redis: {e}")
            raise
    
    async def close(self) -> None:
        """Fecha a conexão com Redis."""
        if self.redis_client:
            await self.redis_client.close()
    
    def _get_key(self, entry_id: str) -> str:
        """Gera chave Redis para entrada."""
        return f"mcp_memory:{entry_id}"
    
    def _get_user_key(self, user_id: str) -> str:
        """Gera chave Redis para índice de usuário."""
        return f"mcp_memory:user:{user_id}"
    
    def _get_session_key(self, session_id: str) -> str:
        """Gera chave Redis para índice de sessão."""
        return f"mcp_memory:session:{session_id}"
    
    async def store(self, entry: MemoryEntry) -> bool:
        """Armazena uma entrada de memória no Redis."""
        try:
            # Serializar entrada
            data = json.dumps(entry.to_dict())
            
            # Calcular TTL
            ttl = None
            if entry.expires_at:
                ttl = int((entry.expires_at - datetime.utcnow()).total_seconds())
                if ttl <= 0:
                    return False  # Já expirado
            
            # Armazenar entrada principal
            key = self._get_key(entry.id)
            if ttl:
                await self.redis_client.setex(key, ttl, data)
            else:
                await self.redis_client.set(key, data)
            
            # Adicionar aos índices
            user_key = self._get_user_key(entry.user_id)
            await self.redis_client.sadd(user_key, entry.id)
            
            session_key = self._get_session_key(entry.session_id)
            await self.redis_client.sadd(session_key, entry.id)
            
            # Índices por tags
            for tag in entry.tags:
                tag_key = f"mcp_memory:tag:{tag}"
                await self.redis_client.sadd(tag_key, entry.id)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao armazenar memória: {e}")
            return False
    
    async def retrieve(self, entry_id: str) -> Optional[MemoryEntry]:
        """Recupera uma entrada de memória do Redis."""
        try:
            key = self._get_key(entry_id)
            data = await self.redis_client.get(key)
            
            if not data:
                return None
            
            entry_dict = json.loads(data)
            return MemoryEntry.from_dict(entry_dict)
            
        except Exception as e:
            logger.error(f"❌ Erro ao recuperar memória: {e}")
            return None
    
    async def search(
        self, 
        user_id: str, 
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """Busca entradas de memória no Redis."""
        try:
            # Começar com entradas do usuário
            user_key = self._get_user_key(user_id)
            entry_ids = await self.redis_client.smembers(user_key)
            
            # Filtrar por sessão se especificado
            if session_id:
                session_key = self._get_session_key(session_id)
                session_ids = await self.redis_client.smembers(session_key)
                entry_ids = entry_ids.intersection(session_ids)
            
            # Filtrar por tags se especificado
            if tags:
                for tag in tags:
                    tag_key = f"mcp_memory:tag:{tag}"
                    tag_ids = await self.redis_client.smembers(tag_key)
                    entry_ids = entry_ids.intersection(tag_ids)
            
            # Recuperar entradas
            entries = []
            for entry_id in list(entry_ids)[:limit]:
                entry = await self.retrieve(entry_id)
                if entry:
                    entries.append(entry)
            
            # Ordenar por data de atualização (mais recente primeiro)
            entries.sort(key=lambda x: x.updated_at, reverse=True)
            
            return entries
            
        except Exception as e:
            logger.error(f"❌ Erro ao buscar memórias: {e}")
            return []
    
    async def delete(self, entry_id: str) -> bool:
        """Remove uma entrada de memória do Redis."""
        try:
            # Recuperar entrada para obter metadados
            entry = await self.retrieve(entry_id)
            if not entry:
                return False
            
            # Remover entrada principal
            key = self._get_key(entry_id)
            await self.redis_client.delete(key)
            
            # Remover dos índices
            user_key = self._get_user_key(entry.user_id)
            await self.redis_client.srem(user_key, entry_id)
            
            session_key = self._get_session_key(entry.session_id)
            await self.redis_client.srem(session_key, entry_id)
            
            # Remover dos índices de tags
            for tag in entry.tags:
                tag_key = f"mcp_memory:tag:{tag}"
                await self.redis_client.srem(tag_key, entry_id)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao deletar memória: {e}")
            return False
    
    async def cleanup_expired(self) -> int:
        """Remove entradas expiradas do Redis."""
        # Redis remove automaticamente chaves com TTL
        # Este método pode ser usado para limpeza adicional se necessário
        return 0


class MCPMemoryManager:
    """Gerenciador principal do sistema MCP Memory."""
    
    def __init__(self):
        self.settings = get_settings()
        self.provider: Optional[MemoryProvider] = None
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Inicializa o gerenciador de memória."""
        if not self.settings.mcp_memory_enabled:
            logger.info("🔇 MCP Memory desabilitado")
            return
        
        # Criar provedor baseado na configuração
        if self.settings.mcp_memory_provider == "redis":
            self.provider = RedisMemoryProvider(
                self.settings.redis_url,
                self.settings.redis_password
            )
            await self.provider.initialize()
        else:
            raise ValueError(f"Provedor de memória não suportado: {self.settings.mcp_memory_provider}")
        
        # Iniciar tarefa de limpeza
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("✅ MCP Memory Manager inicializado")
    
    async def close(self) -> None:
        """Fecha o gerenciador de memória."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self.provider:
            await self.provider.close()
    
    async def store_memory(
        self,
        user_id: str,
        session_id: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        ttl_seconds: Optional[int] = None
    ) -> str:
        """
        Armazena uma nova memória.
        
        Returns:
            ID da entrada criada
        """
        if not self.provider:
            raise RuntimeError("MCP Memory não inicializado")
        
        # Gerar ID único
        entry_id = f"{user_id}:{session_id}:{datetime.utcnow().timestamp()}"
        
        # Calcular expiração
        expires_at = None
        if ttl_seconds:
            expires_at = datetime.utcnow() + timedelta(seconds=ttl_seconds)
        elif self.settings.mcp_memory_ttl > 0:
            expires_at = datetime.utcnow() + timedelta(seconds=self.settings.mcp_memory_ttl)
        
        # Criar entrada
        entry = MemoryEntry(
            id=entry_id,
            user_id=user_id,
            session_id=session_id,
            content=content,
            metadata=metadata or {},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            expires_at=expires_at,
            tags=tags or []
        )
        
        # Armazenar
        success = await self.provider.store(entry)
        if not success:
            raise RuntimeError("Falha ao armazenar memória")
        
        return entry_id
    
    async def get_memory(self, entry_id: str) -> Optional[MemoryEntry]:
        """Recupera uma memória por ID."""
        if not self.provider:
            return None
        
        return await self.provider.retrieve(entry_id)
    
    async def search_memories(
        self,
        user_id: str,
        session_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """Busca memórias do usuário."""
        if not self.provider:
            return []
        
        return await self.provider.search(user_id, session_id, tags, limit)
    
    async def delete_memory(self, entry_id: str) -> bool:
        """Remove uma memória."""
        if not self.provider:
            return False
        
        return await self.provider.delete(entry_id)
    
    async def _cleanup_loop(self) -> None:
        """Loop de limpeza de memórias expiradas."""
        while True:
            try:
                await asyncio.sleep(3600)  # Executar a cada hora
                if self.provider:
                    cleaned = await self.provider.cleanup_expired()
                    if cleaned > 0:
                        logger.info(f"🧹 Limpeza: {cleaned} memórias expiradas removidas")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Erro na limpeza de memórias: {e}")
