"""
Sistema de logging configurável para o Chat IA Agno Financeiro.
"""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger


def setup_logging(
    log_level: str = "INFO",
    environment: str = "development",
    log_file: Optional[str] = None,
    log_format: str = "json",
    log_rotation: str = "1 day",
    log_retention: str = "30 days",
) -> None:
    """
    Configura o sistema de logging da aplicação.
    
    Args:
        log_level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Ambiente de execução (development, production)
        log_file: Caminho para o arquivo de log
        log_format: Formato do log (json, text)
        log_rotation: Rotação dos logs
        log_retention: Retenção dos logs
    """
    # Remove handlers padrão
    logger.remove()
    
    # Formato para desenvolvimento
    dev_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # Formato para produção (JSON)
    prod_format = (
        "{{\"timestamp\": \"{time:YYYY-MM-DD HH:mm:ss.SSS}\", "
        "\"level\": \"{level}\", "
        "\"logger\": \"{name}\", "
        "\"function\": \"{function}\", "
        "\"line\": {line}, "
        "\"message\": \"{message}\"}}"
    )
    
    # Escolher formato baseado no ambiente
    if environment == "development":
        format_string = dev_format
        colorize = True
    else:
        format_string = prod_format if log_format == "json" else dev_format
        colorize = False
    
    # Handler para console
    logger.add(
        sys.stdout,
        format=format_string,
        level=log_level,
        colorize=colorize,
        backtrace=True,
        diagnose=environment == "development",
    )
    
    # Handler para arquivo (se especificado)
    if log_file:
        # Criar diretório se não existir
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            format=prod_format if log_format == "json" else dev_format,
            level=log_level,
            rotation=log_rotation,
            retention=log_retention,
            compression="gz",
            backtrace=True,
            diagnose=environment == "development",
        )
    
    # Log inicial
    logger.info(f"🔧 Logging configurado - Nível: {log_level}, Ambiente: {environment}")
    
    if log_file:
        logger.info(f"📁 Logs salvos em: {log_file}")


def get_logger(name: str):
    """
    Retorna um logger configurado para o módulo especificado.
    
    Args:
        name: Nome do módulo/classe
        
    Returns:
        Logger configurado
    """
    return logger.bind(name=name)
