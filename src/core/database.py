"""
Configuração e gerenciamento do banco de dados.
"""

from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import StaticPool
from loguru import logger

from src.core.config import get_settings

# Base para modelos SQLAlchemy
Base = declarative_base()

# Metadados para migrations
metadata = MetaData()

# Variáveis globais para engines e sessions
async_engine = None
async_session_factory = None


def get_database_url(async_driver: bool = True) -> str:
    """
    Retorna a URL do banco de dados.
    
    Args:
        async_driver: Se deve usar driver assíncrono
        
    Returns:
        URL do banco de dados
    """
    settings = get_settings()
    url = settings.database_url
    
    if async_driver and url.startswith("postgresql://"):
        # Converter para asyncpg
        url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
    elif not async_driver and url.startswith("postgresql+asyncpg://"):
        # Converter para psycopg2
        url = url.replace("postgresql+asyncpg://", "postgresql://", 1)
    
    return url


async def init_db() -> None:
    """Inicializa o banco de dados."""
    global async_engine, async_session_factory
    
    settings = get_settings()
    database_url = get_database_url(async_driver=True)
    
    logger.info(f"🔗 Conectando ao banco de dados...")
    
    # Criar engine assíncrono
    async_engine = create_async_engine(
        database_url,
        pool_size=settings.database_pool_size,
        max_overflow=settings.database_max_overflow,
        echo=settings.environment == "development",
        future=True,
    )
    
    # Criar factory de sessões
    async_session_factory = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    # Criar tabelas (em produção, usar Alembic)
    if settings.environment == "development":
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ Tabelas criadas/verificadas")
    
    logger.info("✅ Banco de dados inicializado")


async def close_db() -> None:
    """Fecha as conexões do banco de dados."""
    global async_engine
    
    if async_engine:
        await async_engine.dispose()
        logger.info("✅ Conexões do banco de dados fechadas")


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager para sessões do banco de dados.
    
    Yields:
        Sessão do banco de dados
    """
    if not async_session_factory:
        raise RuntimeError("Banco de dados não inicializado. Chame init_db() primeiro.")
    
    async with async_session_factory() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency para FastAPI - retorna sessão do banco de dados.
    
    Yields:
        Sessão do banco de dados
    """
    async with get_db_session() as session:
        yield session


class DatabaseManager:
    """Gerenciador de operações do banco de dados."""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
    
    async def initialize(self) -> None:
        """Inicializa o gerenciador."""
        await init_db()
        self.engine = async_engine
        self.session_factory = async_session_factory
    
    async def close(self) -> None:
        """Fecha o gerenciador."""
        await close_db()
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Retorna uma sessão do banco de dados."""
        async with get_db_session() as session:
            yield session
    
    async def health_check(self) -> bool:
        """
        Verifica a saúde da conexão com o banco.
        
        Returns:
            True se a conexão está saudável
        """
        try:
            async with self.get_session() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"❌ Health check do banco falhou: {e}")
            return False
