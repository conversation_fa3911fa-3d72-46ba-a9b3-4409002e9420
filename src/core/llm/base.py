"""
Classes base para integração com LLMs.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from enum import Enum


class LLMProvider(Enum):
    """Provedores de LLM suportados."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"


class TaskComplexity(Enum):
    """Níveis de complexidade de tarefas."""
    SIMPLE = "simple"      # Respostas diretas, consultas básicas
    MEDIUM = "medium"      # Análises, comparações, cálculos
    COMPLEX = "complex"    # Análises profundas, relatórios, estratégias


@dataclass
class LLMMessage:
    """Mensagem para LLM."""
    role: str  # system, user, assistant
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """Resposta do LLM."""
    content: str
    provider: LLMProvider
    model: str
    tokens_used: int
    cost_estimate: float
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMConfig:
    """Configuração para LLM."""
    provider: LLMProvider
    model: str
    max_tokens: int
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: Optional[List[str]] = None


class LLMClient(ABC):
    """Interface abstrata para clientes LLM."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
    
    @abstractmethod
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Gera resposta usando o LLM."""
        pass
    
    @abstractmethod
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ):
        """Gera resposta em streaming."""
        pass
    
    @abstractmethod
    def estimate_cost(self, tokens: int) -> float:
        """Estima o custo baseado no número de tokens."""
        pass
    
    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """Conta tokens no texto."""
        pass


class LLMRouter:
    """Roteador inteligente para seleção de LLM baseado na tarefa."""
    
    def __init__(self):
        self.clients: Dict[LLMProvider, LLMClient] = {}
        self.routing_rules = self._setup_routing_rules()
    
    def register_client(self, provider: LLMProvider, client: LLMClient):
        """Registra um cliente LLM."""
        self.clients[provider] = client
    
    def _setup_routing_rules(self) -> Dict[TaskComplexity, List[LLMProvider]]:
        """Define regras de roteamento baseadas na complexidade."""
        return {
            TaskComplexity.SIMPLE: [
                LLMProvider.OPENAI,    # GPT-4 Mini - rápido e barato
                LLMProvider.GOOGLE,    # Gemini Flash - alternativa rápida
                LLMProvider.ANTHROPIC  # Claude - fallback
            ],
            TaskComplexity.MEDIUM: [
                LLMProvider.GOOGLE,    # Gemini Pro - bom custo-benefício
                LLMProvider.OPENAI,    # GPT-4 - qualidade consistente
                LLMProvider.ANTHROPIC  # Claude - análise detalhada
            ],
            TaskComplexity.COMPLEX: [
                LLMProvider.ANTHROPIC, # Claude - melhor para análises profundas
                LLMProvider.OPENAI,    # GPT-4 - versatilidade
                LLMProvider.GOOGLE     # Gemini Pro - contexto longo
            ]
        }
    
    def select_provider(
        self,
        complexity: TaskComplexity,
        context_length: int = 0,
        cost_priority: bool = False
    ) -> LLMProvider:
        """
        Seleciona o melhor provedor para a tarefa.
        
        Args:
            complexity: Complexidade da tarefa
            context_length: Tamanho do contexto em tokens
            cost_priority: Se deve priorizar custo baixo
            
        Returns:
            Provedor selecionado
        """
        candidates = self.routing_rules[complexity]
        
        # Filtrar provedores disponíveis
        available = [p for p in candidates if p in self.clients]
        
        if not available:
            raise RuntimeError("Nenhum provedor LLM disponível")
        
        # Lógica de seleção
        if cost_priority and complexity == TaskComplexity.SIMPLE:
            # Para tarefas simples com prioridade de custo, usar o mais barato
            return available[0]
        
        if context_length > 100000:
            # Para contextos muito longos, preferir Gemini
            if LLMProvider.GOOGLE in available:
                return LLMProvider.GOOGLE
        
        # Retornar primeira opção disponível
        return available[0]
    
    async def generate(
        self,
        messages: List[LLMMessage],
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        provider: Optional[LLMProvider] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Gera resposta usando o LLM mais apropriado.
        
        Args:
            messages: Mensagens para o LLM
            complexity: Complexidade da tarefa
            provider: Provedor específico (opcional)
            **kwargs: Argumentos adicionais
            
        Returns:
            Resposta do LLM
        """
        # Selecionar provedor se não especificado
        if provider is None:
            context_length = sum(len(msg.content) for msg in messages)
            provider = self.select_provider(
                complexity,
                context_length,
                kwargs.get('cost_priority', False)
            )
        
        # Obter cliente
        client = self.clients.get(provider)
        if not client:
            raise RuntimeError(f"Cliente {provider} não disponível")
        
        # Gerar resposta
        return await client.generate(messages, **kwargs)
    
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        provider: Optional[LLMProvider] = None,
        **kwargs
    ):
        """Gera resposta em streaming."""
        # Selecionar provedor se não especificado
        if provider is None:
            context_length = sum(len(msg.content) for msg in messages)
            provider = self.select_provider(
                complexity,
                context_length,
                kwargs.get('cost_priority', False)
            )
        
        # Obter cliente
        client = self.clients.get(provider)
        if not client:
            raise RuntimeError(f"Cliente {provider} não disponível")
        
        # Gerar resposta em streaming
        async for chunk in client.stream_generate(messages, **kwargs):
            yield chunk


# Estimativas de custo por 1K tokens (valores aproximados em USD)
COST_ESTIMATES = {
    LLMProvider.OPENAI: {
        "gpt-4-1106-preview": {"input": 0.01, "output": 0.03},
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-3.5-turbo": {"input": 0.001, "output": 0.002},
    },
    LLMProvider.ANTHROPIC: {
        "claude-3-sonnet-20240229": {"input": 0.003, "output": 0.015},
        "claude-3-haiku-20240307": {"input": 0.00025, "output": 0.00125},
    },
    LLMProvider.GOOGLE: {
        "gemini-pro": {"input": 0.0005, "output": 0.0015},
        "gemini-pro-vision": {"input": 0.0005, "output": 0.0015},
    }
}
