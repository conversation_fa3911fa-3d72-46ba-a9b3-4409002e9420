"""
Cliente OpenAI para integração com GPT-4 e outros modelos.
"""

import tiktoken
from typing import List, Optional, AsyncGenerator
from openai import AsyncOpenAI
from loguru import logger

from src.core.config import get_settings
from src.core.llm.base import (
    LLMClient, LLMConfig, LLMMessage, LLMResponse, 
    LLMProvider, COST_ESTIMATES
)


class OpenAIClient(LLMClient):
    """Cliente para OpenAI GPT models."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        settings = get_settings()
        
        self.client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            timeout=60.0,
        )
        
        # Encoder para contagem de tokens
        try:
            self.encoder = tiktoken.encoding_for_model(config.model)
        except KeyError:
            # Fallback para modelos não reconhecidos
            self.encoder = tiktoken.get_encoding("cl100k_base")
        
        logger.info(f"✅ OpenAI Client inicializado - Modelo: {config.model}")
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[dict]:
        """Converte mensagens para formato OpenAI."""
        return [
            {
                "role": msg.role,
                "content": msg.content
            }
            for msg in messages
        ]
    
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Gera resposta usando OpenAI."""
        try:
            # Preparar mensagens
            openai_messages = self._convert_messages(messages)
            
            # Parâmetros da requisição
            request_params = {
                "model": self.config.model,
                "messages": openai_messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "frequency_penalty": self.config.frequency_penalty,
                "presence_penalty": self.config.presence_penalty,
            }
            
            # Adicionar stop sequences se especificado
            if self.config.stop_sequences:
                request_params["stop"] = self.config.stop_sequences
            
            # Sobrescrever com kwargs
            request_params.update(kwargs)
            
            # Fazer requisição
            response = await self.client.chat.completions.create(**request_params)
            
            # Extrair resposta
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            # Estimar custo
            cost_estimate = self.estimate_cost(tokens_used)
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.OPENAI,
                model=self.config.model,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate,
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Erro na geração OpenAI: {e}")
            raise
    
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Gera resposta em streaming usando OpenAI."""
        try:
            # Preparar mensagens
            openai_messages = self._convert_messages(messages)
            
            # Parâmetros da requisição
            request_params = {
                "model": self.config.model,
                "messages": openai_messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "frequency_penalty": self.config.frequency_penalty,
                "presence_penalty": self.config.presence_penalty,
                "stream": True,
            }
            
            # Adicionar stop sequences se especificado
            if self.config.stop_sequences:
                request_params["stop"] = self.config.stop_sequences
            
            # Sobrescrever com kwargs
            request_params.update(kwargs)
            
            # Fazer requisição streaming
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"❌ Erro no streaming OpenAI: {e}")
            raise
    
    def estimate_cost(self, tokens: int) -> float:
        """Estima o custo baseado no número de tokens."""
        costs = COST_ESTIMATES.get(LLMProvider.OPENAI, {}).get(self.config.model)
        
        if not costs:
            # Fallback para GPT-4 se modelo não encontrado
            costs = COST_ESTIMATES[LLMProvider.OPENAI]["gpt-4"]
        
        # Assumir 75% input, 25% output (aproximação)
        input_tokens = int(tokens * 0.75)
        output_tokens = int(tokens * 0.25)
        
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        
        return input_cost + output_cost
    
    def count_tokens(self, text: str) -> int:
        """Conta tokens no texto usando tiktoken."""
        try:
            return len(self.encoder.encode(text))
        except Exception as e:
            logger.warning(f"⚠️ Erro ao contar tokens: {e}")
            # Fallback: aproximação de 4 caracteres por token
            return len(text) // 4


def create_openai_client() -> OpenAIClient:
    """Factory function para criar cliente OpenAI."""
    settings = get_settings()
    
    config = LLMConfig(
        provider=LLMProvider.OPENAI,
        model=settings.openai_model,
        max_tokens=settings.openai_max_tokens,
        temperature=0.7,
        top_p=1.0,
        frequency_penalty=0.0,
        presence_penalty=0.0,
    )
    
    return OpenAIClient(config)
