"""
Gerenciador principal do sistema de LLMs.
"""

from typing import Dict, List, Optional
from loguru import logger

from src.core.config import get_settings
from src.core.llm.base import <PERSON><PERSON><PERSON><PERSON>, LLMProvider, TaskComplexity, LLMMessage, LLMResponse
from src.core.llm.openai_client import create_openai_client
from src.core.llm.anthropic_client import create_anthropic_client
from src.core.llm.google_client import create_google_client


class LLMManager:
    """Gerenciador principal para todos os LLMs."""
    
    def __init__(self):
        self.router = LLMRouter()
        self.settings = get_settings()
        self._initialized = False
    
    async def initialize(self) -> None:
        """Inicializa todos os clientes LLM disponíveis."""
        if self._initialized:
            return
        
        logger.info("🤖 Inicializando LLM Manager...")
        
        # Inicializar clientes baseado nas chaves de API disponíveis
        clients_initialized = 0
        
        # OpenAI
        try:
            if self.settings.openai_api_key:
                openai_client = create_openai_client()
                self.router.register_client(LLMProvider.OPENAI, openai_client)
                clients_initialized += 1
                logger.info("✅ OpenAI client registrado")
        except Exception as e:
            logger.warning(f"⚠️ Falha ao inicializar OpenAI: {e}")
        
        # Anthropic
        try:
            if self.settings.anthropic_api_key:
                anthropic_client = create_anthropic_client()
                self.router.register_client(LLMProvider.ANTHROPIC, anthropic_client)
                clients_initialized += 1
                logger.info("✅ Anthropic client registrado")
        except Exception as e:
            logger.warning(f"⚠️ Falha ao inicializar Anthropic: {e}")
        
        # Google
        try:
            if self.settings.google_api_key:
                google_client = create_google_client()
                self.router.register_client(LLMProvider.GOOGLE, google_client)
                clients_initialized += 1
                logger.info("✅ Google client registrado")
        except Exception as e:
            logger.warning(f"⚠️ Falha ao inicializar Google: {e}")
        
        if clients_initialized == 0:
            raise RuntimeError("❌ Nenhum cliente LLM foi inicializado. Verifique as chaves de API.")
        
        self._initialized = True
        logger.info(f"🎯 LLM Manager inicializado com {clients_initialized} clientes")
    
    async def generate_response(
        self,
        messages: List[LLMMessage],
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        provider: Optional[LLMProvider] = None,
        cost_priority: bool = False,
        **kwargs
    ) -> LLMResponse:
        """
        Gera resposta usando o LLM mais apropriado.
        
        Args:
            messages: Lista de mensagens para o LLM
            complexity: Complexidade da tarefa
            provider: Provedor específico (opcional)
            cost_priority: Se deve priorizar custo baixo
            **kwargs: Argumentos adicionais para o LLM
            
        Returns:
            Resposta do LLM
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.router.generate(
            messages=messages,
            complexity=complexity,
            provider=provider,
            cost_priority=cost_priority,
            **kwargs
        )
    
    async def stream_response(
        self,
        messages: List[LLMMessage],
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        provider: Optional[LLMProvider] = None,
        cost_priority: bool = False,
        **kwargs
    ):
        """
        Gera resposta em streaming usando o LLM mais apropriado.
        
        Args:
            messages: Lista de mensagens para o LLM
            complexity: Complexidade da tarefa
            provider: Provedor específico (opcional)
            cost_priority: Se deve priorizar custo baixo
            **kwargs: Argumentos adicionais para o LLM
            
        Yields:
            Chunks da resposta
        """
        if not self._initialized:
            await self.initialize()
        
        async for chunk in self.router.stream_generate(
            messages=messages,
            complexity=complexity,
            provider=provider,
            cost_priority=cost_priority,
            **kwargs
        ):
            yield chunk
    
    def get_available_providers(self) -> List[LLMProvider]:
        """Retorna lista de provedores disponíveis."""
        return list(self.router.clients.keys())
    
    def is_provider_available(self, provider: LLMProvider) -> bool:
        """Verifica se um provedor está disponível."""
        return provider in self.router.clients
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Verifica a saúde de todos os clientes LLM.
        
        Returns:
            Dicionário com status de cada provedor
        """
        if not self._initialized:
            await self.initialize()
        
        health_status = {}
        
        for provider in self.get_available_providers():
            try:
                # Teste simples com cada provedor
                test_messages = [
                    LLMMessage(role="user", content="Hello, respond with 'OK'")
                ]
                
                response = await self.generate_response(
                    messages=test_messages,
                    provider=provider,
                    complexity=TaskComplexity.SIMPLE,
                    max_tokens=10
                )
                
                health_status[provider.value] = "OK" in response.content.upper()
                
            except Exception as e:
                logger.error(f"❌ Health check falhou para {provider.value}: {e}")
                health_status[provider.value] = False
        
        return health_status


# Instância global do gerenciador
llm_manager = LLMManager()


async def get_llm_manager() -> LLMManager:
    """Dependency para FastAPI - retorna o gerenciador de LLMs."""
    if not llm_manager._initialized:
        await llm_manager.initialize()
    return llm_manager


def create_system_message(content: str) -> LLMMessage:
    """Helper para criar mensagem de sistema."""
    return LLMMessage(role="system", content=content)


def create_user_message(content: str) -> LLMMessage:
    """Helper para criar mensagem de usuário."""
    return LLMMessage(role="user", content=content)


def create_assistant_message(content: str) -> LLMMessage:
    """Helper para criar mensagem de assistente."""
    return LLMMessage(role="assistant", content=content)
