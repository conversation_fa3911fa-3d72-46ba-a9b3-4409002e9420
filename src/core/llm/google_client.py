"""
Cliente Google para integração com Gemini.
"""

from typing import List, Optional, AsyncGenerator
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from loguru import logger

from src.core.config import get_settings
from src.core.llm.base import (
    LLMClient, LLMConfig, LLMMessage, LLMResponse, 
    LLMProvider, COST_ESTIMATES
)


class GoogleClient(LLMClient):
    """Cliente para Google Gemini models."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        settings = get_settings()
        
        # Configurar API key
        genai.configure(api_key=settings.google_api_key)
        
        # Configurações de segurança (mais permissivas para análise financeira)
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        # Configurações de geração
        self.generation_config = genai.types.GenerationConfig(
            temperature=config.temperature,
            top_p=config.top_p,
            max_output_tokens=config.max_tokens,
            stop_sequences=config.stop_sequences,
        )
        
        # Criar modelo
        self.model = genai.GenerativeModel(
            model_name=config.model,
            generation_config=self.generation_config,
            safety_settings=self.safety_settings,
        )
        
        logger.info(f"✅ Google Client inicializado - Modelo: {config.model}")
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[dict]:
        """Converte mensagens para formato Google."""
        converted = []
        
        for msg in messages:
            if msg.role == "system":
                # Gemini não tem role "system", converter para "user"
                converted.append({
                    "role": "user",
                    "parts": [f"[SYSTEM] {msg.content}"]
                })
            elif msg.role == "assistant":
                converted.append({
                    "role": "model",
                    "parts": [msg.content]
                })
            else:  # user
                converted.append({
                    "role": "user",
                    "parts": [msg.content]
                })
        
        return converted
    
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Gera resposta usando Google Gemini."""
        try:
            # Preparar mensagens
            gemini_messages = self._convert_messages(messages)
            
            # Se há apenas uma mensagem, usar generate_content
            if len(gemini_messages) == 1:
                response = await self.model.generate_content_async(
                    gemini_messages[0]["parts"][0]
                )
            else:
                # Para conversas, usar chat
                chat = self.model.start_chat(history=gemini_messages[:-1])
                response = await chat.send_message_async(
                    gemini_messages[-1]["parts"][0]
                )
            
            # Extrair resposta
            content = response.text
            
            # Contar tokens (aproximação)
            tokens_used = self.count_tokens(content) + sum(
                self.count_tokens(msg.content) for msg in messages
            )
            
            # Estimar custo
            cost_estimate = self.estimate_cost(tokens_used)
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.GOOGLE,
                model=self.config.model,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate,
                metadata={
                    "finish_reason": response.candidates[0].finish_reason.name if response.candidates else None,
                    "safety_ratings": [
                        {
                            "category": rating.category.name,
                            "probability": rating.probability.name
                        }
                        for rating in response.candidates[0].safety_ratings
                    ] if response.candidates else [],
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Erro na geração Google: {e}")
            raise
    
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Gera resposta em streaming usando Google Gemini."""
        try:
            # Preparar mensagens
            gemini_messages = self._convert_messages(messages)
            
            # Se há apenas uma mensagem, usar generate_content com stream
            if len(gemini_messages) == 1:
                response_stream = await self.model.generate_content_async(
                    gemini_messages[0]["parts"][0],
                    stream=True
                )
            else:
                # Para conversas, usar chat com stream
                chat = self.model.start_chat(history=gemini_messages[:-1])
                response_stream = await chat.send_message_async(
                    gemini_messages[-1]["parts"][0],
                    stream=True
                )
            
            async for chunk in response_stream:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"❌ Erro no streaming Google: {e}")
            raise
    
    def estimate_cost(self, tokens: int) -> float:
        """Estima o custo baseado no número de tokens."""
        costs = COST_ESTIMATES.get(LLMProvider.GOOGLE, {}).get(self.config.model)
        
        if not costs:
            # Fallback para Gemini Pro se modelo não encontrado
            costs = COST_ESTIMATES[LLMProvider.GOOGLE]["gemini-pro"]
        
        # Assumir 75% input, 25% output (aproximação)
        input_tokens = int(tokens * 0.75)
        output_tokens = int(tokens * 0.25)
        
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        
        return input_cost + output_cost
    
    def count_tokens(self, text: str) -> int:
        """
        Conta tokens no texto.
        
        Nota: Google não fornece uma biblioteca oficial de tokenização para Gemini,
        então usamos uma aproximação baseada no número de caracteres.
        """
        try:
            # Tentar usar o método oficial se disponível
            response = self.model.count_tokens(text)
            return response.total_tokens
        except Exception:
            # Fallback: aproximação de 4 caracteres por token
            return len(text) // 4


def create_google_client() -> GoogleClient:
    """Factory function para criar cliente Google."""
    settings = get_settings()
    
    config = LLMConfig(
        provider=LLMProvider.GOOGLE,
        model=settings.google_model,
        max_tokens=settings.google_max_tokens,
        temperature=0.7,
        top_p=1.0,
    )
    
    return GoogleClient(config)
