"""
Cliente Anthropic para integração com Claude.
"""

from typing import List, Optional, AsyncGenerator
from anthropic import As<PERSON><PERSON><PERSON>hropic
from loguru import logger

from src.core.config import get_settings
from src.core.llm.base import (
    LLMClient, LLMConfig, LLMMessage, LLMResponse, 
    LLMProvider, COST_ESTIMATES
)


class AnthropicClient(LLMClient):
    """Cliente para Anthropic Claude models."""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        settings = get_settings()
        
        self.client = AsyncAnthropic(
            api_key=settings.anthropic_api_key,
            timeout=60.0,
        )
        
        logger.info(f"✅ Anthropic Client inicializado - Modelo: {config.model}")
    
    def _convert_messages(self, messages: List[LLMMessage]) -> tuple:
        """
        Converte mensagens para formato Anthropic.
        
        Returns:
            Tuple (system_message, conversation_messages)
        """
        system_message = ""
        conversation_messages = []
        
        for msg in messages:
            if msg.role == "system":
                system_message += msg.content + "\n"
            else:
                conversation_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        return system_message.strip(), conversation_messages
    
    async def generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Gera resposta usando Anthropic Claude."""
        try:
            # Preparar mensagens
            system_message, conversation_messages = self._convert_messages(messages)
            
            # Parâmetros da requisição
            request_params = {
                "model": self.config.model,
                "messages": conversation_messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
            }
            
            # Adicionar system message se presente
            if system_message:
                request_params["system"] = system_message
            
            # Adicionar stop sequences se especificado
            if self.config.stop_sequences:
                request_params["stop_sequences"] = self.config.stop_sequences
            
            # Sobrescrever com kwargs
            request_params.update(kwargs)
            
            # Fazer requisição
            response = await self.client.messages.create(**request_params)
            
            # Extrair resposta
            content = response.content[0].text
            tokens_used = response.usage.input_tokens + response.usage.output_tokens
            
            # Estimar custo
            cost_estimate = self._estimate_cost_detailed(
                response.usage.input_tokens,
                response.usage.output_tokens
            )
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.ANTHROPIC,
                model=self.config.model,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate,
                metadata={
                    "stop_reason": response.stop_reason,
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens,
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Erro na geração Anthropic: {e}")
            raise
    
    async def stream_generate(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Gera resposta em streaming usando Anthropic Claude."""
        try:
            # Preparar mensagens
            system_message, conversation_messages = self._convert_messages(messages)
            
            # Parâmetros da requisição
            request_params = {
                "model": self.config.model,
                "messages": conversation_messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "stream": True,
            }
            
            # Adicionar system message se presente
            if system_message:
                request_params["system"] = system_message
            
            # Adicionar stop sequences se especificado
            if self.config.stop_sequences:
                request_params["stop_sequences"] = self.config.stop_sequences
            
            # Sobrescrever com kwargs
            request_params.update(kwargs)
            
            # Fazer requisição streaming
            stream = await self.client.messages.create(**request_params)
            
            async for chunk in stream:
                if chunk.type == "content_block_delta":
                    if hasattr(chunk.delta, 'text'):
                        yield chunk.delta.text
                        
        except Exception as e:
            logger.error(f"❌ Erro no streaming Anthropic: {e}")
            raise
    
    def _estimate_cost_detailed(self, input_tokens: int, output_tokens: int) -> float:
        """Estima custo com tokens de input e output separados."""
        costs = COST_ESTIMATES.get(LLMProvider.ANTHROPIC, {}).get(self.config.model)
        
        if not costs:
            # Fallback para Claude Sonnet se modelo não encontrado
            costs = COST_ESTIMATES[LLMProvider.ANTHROPIC]["claude-3-sonnet-20240229"]
        
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        
        return input_cost + output_cost
    
    def estimate_cost(self, tokens: int) -> float:
        """Estima o custo baseado no número de tokens."""
        costs = COST_ESTIMATES.get(LLMProvider.ANTHROPIC, {}).get(self.config.model)
        
        if not costs:
            # Fallback para Claude Sonnet se modelo não encontrado
            costs = COST_ESTIMATES[LLMProvider.ANTHROPIC]["claude-3-sonnet-20240229"]
        
        # Assumir 75% input, 25% output (aproximação)
        input_tokens = int(tokens * 0.75)
        output_tokens = int(tokens * 0.25)
        
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        
        return input_cost + output_cost
    
    def count_tokens(self, text: str) -> int:
        """
        Conta tokens no texto.
        
        Nota: Anthropic não fornece uma biblioteca oficial de tokenização,
        então usamos uma aproximação baseada no número de caracteres.
        """
        # Aproximação: Claude usa aproximadamente 3.5 caracteres por token
        return len(text) // 4


def create_anthropic_client() -> AnthropicClient:
    """Factory function para criar cliente Anthropic."""
    settings = get_settings()
    
    config = LLMConfig(
        provider=LLMProvider.ANTHROPIC,
        model=settings.anthropic_model,
        max_tokens=settings.anthropic_max_tokens,
        temperature=0.7,
        top_p=1.0,
    )
    
    return AnthropicClient(config)
