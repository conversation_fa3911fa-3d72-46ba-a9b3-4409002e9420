"""
Modelo de usuário.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, DateTime, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.core.database import Base


class User(Base):
    """Modelo de usuário do sistema."""
    
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    full_name = Column(String(255), nullable=True)
    
    # Configurações do usuário
    preferred_language = Column(String(10), default="pt", nullable=False)
    preferred_currency = Column(String(10), default="BRL", nullable=False)
    timezone = Column(String(50), default="America/Sao_Paulo", nullable=False)
    
    # Preferências de investimento
    risk_tolerance = Column(String(20), nullable=True)  # conservative, moderate, aggressive
    investment_experience = Column(String(20), nullable=True)  # beginner, intermediate, advanced
    preferred_assets = Column(Text, nullable=True)  # JSON array of preferred asset classes
    
    # Status e metadados
    is_active = Column(Boolean, default=True, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
