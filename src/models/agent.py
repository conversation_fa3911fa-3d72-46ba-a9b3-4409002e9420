"""
Modelos relacionados aos agentes.
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Integer, Float, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSON
import uuid

from src.core.database import Base


class AgentExecution(Base):
    """Modelo de execução de agente."""
    
    __tablename__ = "agent_executions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Identificação
    agent_name = Column(String(100), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Entrada
    query = Column(Text, nullable=False)
    query_type = Column(String(50), nullable=True)
    asset_class = Column(String(50), nullable=True)
    complexity = Column(String(20), nullable=True)
    
    # Processamento
    processing_time_ms = Column(Integer, nullable=True)
    tokens_used = Column(Integer, nullable=True)
    cost_estimate = Column(Float, nullable=True)
    
    # Resultado
    response = Column(Text, nullable=True)
    confidence_score = Column(Float, nullable=True)
    success = Column(Boolean, default=True, nullable=False)
    error_message = Column(Text, nullable=True)
    
    # Dados estruturados
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f"<AgentExecution(id={self.id}, agent='{self.agent_name}', success={self.success})>"
